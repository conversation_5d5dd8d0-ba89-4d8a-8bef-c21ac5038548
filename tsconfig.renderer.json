{
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "composite": true,
    "outDir": "./dist/renderer",
    "rootDir": "./src/renderer",
    "noEmit": false, /* Renderer process needs to emit files */
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "incremental": true,
    "tsBuildInfoFile": "./dist/.tsbuildinfo-renderer",
    "lib": ["DOM", "DOM.Iterable"] /* Add DOM libs for renderer */
  },
  "include": [
    "src/renderer/**/*",
    "src/shared/**/*",
    "src/types/**/*",
    "src/components/**/*",
    "src/hooks/**/*",
    "src/providers/**/*",
    "src/store/**/*",
    "src/utils/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "src/main",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ]
}
