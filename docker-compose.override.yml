version: '3.8'

# Этот файл содержит переопределения для docker-compose.yml,
# предназначенные исключительно для локальной разработки.

services:
  api-gateway:
    # Запускаем сервис в режиме разработки с отслеживанием изменений
    command: npm run dev
    environment:
      - NODE_ENV=development
    volumes:
      # Синхронизируем исходный код с локальной машиной
      - ./services/api-gateway:/usr/src/app
      # Предотвращаем затирание node_modules внутри контейнера локальной папкой
      - /usr/src/app/node_modules

  auth-service:
    # Запускаем сервис в режиме разработки с отслеживанием изменений
    command: npm run dev
    environment:
      - NODE_ENV=development
    volumes:
      # Синхронизируем исходный код с локальной машиной
      - ./services/auth-service:/usr/src/app
      # Предотвращаем затирание node_modules внутри контейнера локальной папкой
      - /usr/src/app/node_modules

  notification-service:
    # Запускаем сервис в режиме разработки с отслеживанием изменений
    command: npm run dev
    environment:
      - NODE_ENV=development
    volumes:
      # Синхронизируем исходный код с локальной машиной
      - ./services/notification-service:/usr/src/app
      # Предотвращаем затирание node_modules внутри контейнера локальной папкой
      - /usr/src/app/node_modules