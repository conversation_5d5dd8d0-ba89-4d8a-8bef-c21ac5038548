import cors from 'cors';
import express from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { z } from 'zod';

import { trace, context, propagation } from '@opentelemetry/api';
import os from 'os';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

const app = express();

// Schema для валидации API-запросов
const apiVersionSchema = z.object({
  'Accept-Version': z.string().regex(/^v\d+(\.\d+)?$/).optional()
});

const requestSchema = z.object({
  body: z.any(),
  query: z.record(z.string()),
  params: z.record(z.string())
});

// Middleware версионирования API
app.use((req, res, next) => {
  const versionHeader = apiVersionSchema.parse(req.headers);
  req.apiVersion = versionHeader['Accept-Version'] || 'v1';
  next();
});

import NodeCache from 'node-cache';
import { createClient } from 'redis';
const localCache = new NodeCache({ stdTTL: 30 });
const redisClient = createClient({ url: process.env.REDIS_URL });

const cacheMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  // Статистика кэширования
  const cacheStats = {
    hits: { local: 0, redis: 0 },
    misses: 0,
    ttl: 300
  };
  if (req.method === 'POST' || req.path.startsWith('/auth')) return next();
  
  const key = `${req.method}_${req.originalUrl}`;
  
  // Circuit breaker pattern for Redis
  if (redisCircuitBreaker.shouldBlock()) {
    return next();
  }

  const localData = localCache.get(key);
  if (localData) {
    cacheStats.hits.local++;
    res.setHeader('X-Cache-Source', 'local');
    return res.json(localData);
  }

  try {
    const redisData = await redisCircuitBreaker.execute(() => redisClient.get(key));
    if (redisData) {
      cacheStats.hits.redis++;
      localCache.set(key, JSON.parse(redisData));
      res.setHeader('X-Cache-Source', 'redis');
      return res.json(JSON.parse(redisData));
    }
    cacheStats.misses++;
  } catch (e) {
    res.locals.span?.recordException(e);
    console.error('Redis error:', e);
  }

  const originalSend = res.send;
  res.send = function (body) {
    if (res.statusCode < 400 && req.method === 'GET') {
      localCache.set(key, body);
      // Динамический TTL на основе типа данных
const systemLoad = os.loadavg()[0] / os.cpus().length;
const dynamicTTL = systemLoad > 0.7 ? 30 : 
                   systemLoad > 0.5 ? 60 : 
                   req.path.includes('/user/') ? 120 : 
                   req.path.includes('/static/') ? 86400 : 300;
redisClient.setEx(key, dynamicTTL, JSON.stringify(body)).catch(console.error);
    }
    return originalSend.call(this, body);
  };

  next();
};




app.use((req, res, next) => {
  const tracer = trace.getTracer('http-server');
  const span = tracer.startSpan('request');
  
  context.with(trace.setSpan(context.active(), span), () => {
    propagation.inject(context.active(), req.headers);
    res.locals.span = span;
    next();
  });

  res.on('finish', () => {
    span.setAttributes({
      'http.method': req.method,
      'http.route': req.path,
      'http.status_code': res.statusCode,
      'http.user_agent': req.headers['user-agent'],
      'http.response_time': Date.now() - span.startTime
    });
    
    if(res.statusCode >= 400) {
      span.addEvent('error', {
        'error.message': res.statusMessage,
        'error.stack': res.locals.error?.stack
      });
    }
    span.end();
  });

  res.on('close', () => {
    span.addEvent('request_aborted');
    span.end();
  });
});

// Security middleware with rotating nonce
app.use((req, res, next) => {
  const rawNonce = crypto.randomBytes(16).toString('hex');
const hmac = crypto.createHmac('sha256', process.env.HMAC_SECRET!);
hmac.update(rawNonce);
res.locals.cspNonce = hmac.digest('hex');
  next();
});

app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", (req, res) => `'nonce-${res.locals.cspNonce}'`, `'sha256-${crypto.createHmac('sha256', process.env.HMAC_SECRET!).update(res.locals.cspNonce).digest('base64')}'`],
        styleSrc: ["'self'", (req, res) => `'nonce-${res.locals.cspNonce}'`, `'sha256-${crypto.createHmac('sha256', process.env.HMAC_SECRET!).update(res.locals.cspNonce).digest('base64')}'`],
        reportUri: '/csp-violation-report',
        imgSrc: ["'self'", 'data:', 'https://*.example.com'],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        frameAncestors: ["'none'"],
      },
    },
  })
);

app.use(
  cors({
    origin: process.env.CORS_ORIGINS || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  })
);

const getDynamicRateLimit = () => {
  const load = os.loadavg()[0];
  const cpuCount = os.cpus().length;
  return load / cpuCount > 0.7 ? 50 : 100;
};

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: getDynamicRateLimit,
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    res
      .status(429)
      .set('Retry-After', Math.ceil(req.rateLimit.resetTime.getTime() / 1000))
      .json({
        error: 'Превышен лимит запросов',
        retryAfter: req.rateLimit.resetTime
      });
  }
});



app.use((req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return res.status(401).json({ error: 'Требуется авторизация' });
  
  const [type, token] = authHeader.split(' ');
  if (type !== 'Bearer' || !token) return res.status(401).json({ error: 'Неверный формат токена' });

  try {
    if (req.path === '/refresh' && req.method === 'POST') {
      const refreshToken = req.cookies?.refreshToken;
      if (!refreshToken) throw new Error('Refresh token required');
      
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET!);
      const storedToken = await redisClient.get(`refresh_${decoded.userId}`);
      if (refreshToken !== storedToken) throw new Error('Invalid refresh token');

      // Генерация новых токенов
      const newAccessToken = jwt.sign(
        { userId: decoded.userId },
        process.env.JWT_SECRET!,
        { expiresIn: '15m' }
      );
      const newRefreshToken = jwt.sign(
        { userId: decoded.userId },
        process.env.JWT_REFRESH_SECRET!,
        { expiresIn: '7d' }
      );

      // Обновление Redis и инвалидация старого токена
      await redisClient
        .multi()
        .del(`refresh_${decoded.userId}`)
        .setEx(`refresh_${decoded.userId}`, 604800, newRefreshToken)
        .exec();

      req.user = decoded;
      res.setHeader('X-New-Access-Token', newAccessToken);
      res.cookie('refreshToken', newRefreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
    } else {
      req.user = jwt.verify(token, process.env.JWT_SECRET!);
    }
    next();
  } catch (e) {
    res.locals.span?.recordException(e);
    const message = e.name === 'TokenExpiredError' ? 'Срок действия токена истёк' : 'Недействительный токен';
    res.status(401).json({ error: message });
  }
});

app.use((req, res, next) => {
  if (req.method === 'GET' && req.user) {
    const csrfToken = crypto.randomBytes(32).toString('hex');
    redisClient.setEx(`csrf_${req.user.userId}`, 3600, csrfToken)
      .catch(err => console.error('Redis CSRF error:', err));
    res.setHeader('X-CSRF-Token', csrfToken);
  }
  next();
});

app.use((req, res, next) => {
  if (req.method === 'POST') {
    const csrfToken = req.headers['x-csrf-token'];
    const userId = req.user?.userId;
    
    if (!csrfToken || !userId) {
      return res.status(403).json({ error: 'Требуется CSRF токен' });
    }

    redisClient.get(`csrf_${userId}`)
      .then(storedToken => {
        if (storedToken !== csrfToken) {
          return Promise.reject('Неверный CSRF токен');
        }
        return redisClient.del(`csrf_${userId}`);
      })
      .then(() => next())
      .catch(err => {
        res.locals.span?.recordException(err);
        res.status(403).json({ error: typeof err === 'string' ? err : 'Ошибка пров��рки CSRF' });
      });
  } else {
    next();
  }
});

app.use(helmet.permittedCrossDomainPolicies());
app.use(helmet.referrerPolicy({ policy: 'same-origin' }));

// Electron-specific headers


app.use((req, res, next) => {
  // Enhanced Electron header validation
  const ipcRateLimiter = rateLimit({
    windowMs: 1000,
    max: 500,
    keyGenerator: () => req.ip,
    handler: () => res.status(429).json({ error: 'Слишком много IPC-запросов' })
  });

  // HMAC validation for IPC messages
  const validateIPCMessage = (body: any) => {
    const hmac = crypto.createHmac('sha256', process.env.IPC_HMAC_SECRET!)
      .update(JSON.stringify(body))
      .digest('hex');
    return hmac === req.headers['x-ipc-integrity'];
  };

  // Worker thread pool for heavy operations
  const workerPool = new WorkerPool({
    workerPath: path.join(__dirname, 'ipc-worker.js'),
    poolSize: os.cpus().length,
    taskTimeout: 5000
  });

  // Buffered IPC message processing
  const ipcBuffer = new CircularBuffer(5000);
  const adaptiveBatchSize = Math.min(200, Math.floor(os.freemem() / 1024 / 1024));
  setInterval(() => {
    const loadFactor = os.loadavg()[0] / os.cpus().length;
    const dynamicInterval = loadFactor > 0.8 ? 500 : 100;
    
    const batch = ipcBuffer.readBatch(adaptiveBatchSize);
    if(batch.length) {
      workerPool.exec('processBatch', batch)
        .then(metrics => {
          redisClient.hincrby('ipc_metrics', 'processed', batch.length);
          if(loadFactor > 0.7) redisClient.expire('ipc_metrics', 3600);
        })
        .catch(err => console.error('IPC worker error:', err));
    }
  }, dynamicInterval);

  // Message validation middleware
  if(req.headers['x-electron-ipc']) {
    ipcRateLimiter(req, res, () => {
      if(!validateIPCMessage(req.body)) {
        return res.status(403).json({ error: 'Invalid IPC HMAC' });
      }
      ipcBuffer.write({
        ...req.body,
        timestamp: Date.now(),
        sessionId: res.locals.cspNonce
      });
      next();
    });
  } else {
    next();
  }
if (req.headers['x-electron-version'] || req.headers['x-electron-platform']) {
  const rotatingSecret = crypto.createHmac('sha256', process.env.ELECTRON_HMAC_SECRET)
    .update(Math.floor(Date.now() / 3600000).toString())
    .digest('hex');
const isValidIntegrity = crypto.createHmac('sha512', rotatingSecret)
    .update(JSON.stringify({
      ...req.headers,
      timestamp: Date.now()
    }))
    .digest('base64') === req.headers['x-electron-integrity'];

  if (!isValidIntegrity) {
    return res.status(403).json({ error: 'Invalid Electron integrity header' });
  }
    if (
      req.headers.origin !== 'file://' ||
      req.get('User-Agent')?.indexOf('Electron') === -1
    ) {
      return res.status(403).json({ error: 'Invalid Electron headers' });
    }
  }
  
  res.setHeader('Electron-Security-Policies', `
    default-src 'self';
    script-src 'self' 'wasm-unsafe-eval' 'nonce-${res.locals.cspNonce}';
    worker-src 'self' blob:;
    connect-src 'self' ws://localhost:*;
    require-trusted-types-for 'script';
  `);
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
  res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Permissions-Policy', 
    'geolocation=(), camera=(), fullscreen=(self), webgpu=(), wasm=()');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Origin-Agent-Cluster', '?1');
  res.setHeader('Document-Policy', 'js-profiling, js-profiling=*');
  res.setHeader('Electron-Sandbox', 'enable');
  res.setHeader('Context-Isolation', 'enable');
  res.setHeader('Preload-Script', `file://${path.join(__dirname, 'preload.js')}`);
  next();
});

app.use(cacheMiddleware);
// Optimized middleware order
app.use(limiter);
app.use(express.json());
app.use(cacheMiddleware);

// Централизованный обработчик ошибок
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  res.locals.span?.recordException(err);
  
  const statusMap = {
    ZodError: 400,
    JsonWebTokenError: 401,
    default: 500
  };
  
  res.status(statusMap[err.name] || 500).json({
    error: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    code: err.name
  });
});


// Healthcheck endpoints
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    dependencies: {
      redis: redisClient.isOpen,
      cache: localCache.keys().length
    }
  });
});

app.get('/metrics', (req, res) => {
  res.json({
    memoryUsage: process.memoryUsage(),
    loadAvg: os.loadavg(),
    cpuCount: os.cpus().length,
    cacheStats: {
      ...cacheStats,
      ttlDistribution: {
        '30s': dynamicTTL === 30 ? 1 : 0,
        '60s': dynamicTTL === 60 ? 1 : 0,
        '2m': dynamicTTL === 120 ? 1 : 0,
        '5m': dynamicTTL === 300 ? 1 : 0,
        '24h': dynamicTTL === 86400 ? 1 : 0
      },
      localHits: localCache.getStats().hits,
      redisHits: redisClient.serverInfo?.total_reads || 0,
      requestTypes: {
        GET: localCache.keys().filter(k => k.startsWith('GET_')).length,
        POST: localCache.keys().filter(k => k.startsWith('POST_')).length,
        PUT: localCache.keys().filter(k => k.startsWith('PUT_')).length,
        DELETE: localCache.keys().filter(k => k.startsWith('DELETE_')).length
      },
      memoryUsage: process.memoryUsage().heapUsed,
      systemFreeMem: os.freemem(),
      systemTotalMem: os.totalmem()
    }
  });
});

// Удаляем дублирующуюся реализацию на строке 185
/*
const validateRequest = (schema: z.ZodSchema) => (req: Request, res: Response, next: NextFunction) => {
  const validationSpan = tracer.startSpan('request.validation');
  try {
    schema.parse({
      body: req.body,
      query: req.query,
      params: req.params
    });
    next();
  } catch (e) {
    res.status(400).json({
      error: 'Ошибка валидации',
      details: e.errors
    });
  }
};
*/