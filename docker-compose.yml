version: '3.8'

services:
  # API Gateway: Точка входа для всех запросов
  api-gateway:
    build:
      context: ./services/api-gateway
      # target: development # Optional: for multi-stage Dockerfiles
    container_name: a14-api-gateway
    ports:
      - "3000:3000"
    env_file: .env
    volumes:
      # Mount source code for live reloading
      - ./services/api-gateway:/app
      # Exclude node_modules from being overwritten by the host
      - /app/node_modules
    depends_on:
      - auth-service
      - event-bus
    networks:
      - a14-network
    command: npm run dev # Command to run dev server with hot-reload

  # Auth Service: Управляет аутентификацией и авторизацией
  auth-service:
    build:
      context: ./services/auth-service
    container_name: a14-auth-service
    env_file: .env
    volumes:
      - ./services/auth-service:/app
      - /app/node_modules
    networks:
      - a14-network
    command: npm run dev

  # Event Bus: Шина событий для асинхронного взаимодействия сервисов
  event-bus:
    image: nats:2.9-alpine
    container_name: a14-event-bus
    ports:
      - "4222:4222" # Клиентский порт
      - "8222:8222" # Порт для мониторинга (HTTP)
    networks:
      - a14-network

  # Notification Service: Отвечает за отправку уведомлений (например, email)
  notification-service:
    build:
      context: ./services/notification-service
    container_name: a14-notification-service
    env_file: .env
    volumes:
      - ./services/notification-service:/app
      - /app/node_modules
    depends_on:
      - event-bus
    networks:
      - a14-network

networks:
  a14-network:
    driver: bridge