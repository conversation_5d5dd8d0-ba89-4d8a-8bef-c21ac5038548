import axios, { AxiosRequestConfig } from 'axios';
import { LRUCache } from 'lru-cache';

const API_BASE_URL = process.env.API_BASE_URL || 'https://api.example.com';
const apiCache = new LRUCache<string, any>({
  max: 500,
  ttl: 60_000
});

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

async function cachedRequest(config: AxiosRequestConfig) {
  const cacheKey = JSON.stringify({
    url: config.url,
    params: config.params,
    data: config.data
  });

  if (apiCache.has(cacheKey)) {
    return { data: apiCache.get(cacheKey) };
  }

  const response = await apiClient(config);
  apiCache.set(cacheKey, response.data);
  return response;
}

export const apiService = {
  get: (url: string, params?: object) => cachedRequest({ method: 'GET', url, params }),
  post: (url: string, data?: object) => cachedRequest({ method: 'POST', url, data })
};