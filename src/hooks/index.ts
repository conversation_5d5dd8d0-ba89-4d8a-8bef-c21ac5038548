/**
 * Unified Hooks Barrel File
 * Re-exports all hooks from their specific modules for easy consumption.
 */

export * from './useDebounce';

export * from './modules/useApi';
export * from './modules/useDOMEvents';
export * from './modules/useForm';
export * from './modules/useLifecycle';
export * from './modules/useStorage';
export * from './modules/useViewport';

// Legacy exports for backward compatibility
import { useApi } from './modules/useApi';
import { useLocalStorage } from './modules/useStorage';
import { useClickOutside } from './modules/useDOMEvents';

/** @deprecated Use `useApi` instead. */
export const useFetch = useApi;

/** @deprecated Use `useLocalStorage` or `useSessionStorage` instead. */
export const useStorage = useLocalStorage;

/** @deprecated Use `useLocalStorage` instead. */
export const usePersistedState = useLocalStorage;

/** @deprecated Use `useClickOutside` instead. */
export const useOnClickOutside = useClickOutside;