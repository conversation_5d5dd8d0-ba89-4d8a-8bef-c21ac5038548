import { useCallback, useEffect, useState } from 'react';

export interface UseApiOptions<TArgs extends any[]> {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  retries?: number;
  retryDelay?: number;
  defaultArgs?: TArgs;
}

export interface UseApiReturn<T, TArgs extends any[]> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (...args: TArgs) => Promise<T>;
  reset: () => void;
}

export function useApi<T = any, TArgs extends any[] = any[]>(
  apiFunction: (...args: TArgs) => Promise<T>,
  options: UseApiOptions<TArgs> = {}
): UseApiReturn<T, TArgs> {
  const {
    immediate = false,
    onSuccess,
    onError,
    retries = 0,
    retryDelay = 1000,
    defaultArgs,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(
    async (...args: TArgs): Promise<T> => {
      setLoading(true);
      setError(null);

      let lastError: Error;
      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          const result = await apiFunction(...args);
          setData(result);
          setLoading(false);
          onSuccess?.(result);
          return result;
        } catch (err) {
          lastError = err as Error;
          if (attempt < retries) {
            await new Promise(resolve => setTimeout(resolve, retryDelay));
          }
        }
      }

      setError(lastError!);
      setLoading(false);
      onError?.(lastError!);
      throw lastError!;
    },
    [apiFunction, retries, retryDelay, onSuccess, onError]
  );

  const reset = useCallback(() => {
    setData(null);
    setLoading(false);
    setError(null);
  }, []);

  useEffect(() => {
    if (immediate) {
      execute(...(defaultArgs || ([] as unknown as TArgs)));
    }
  }, [immediate, execute, defaultArgs]);

  return { data, loading, error, execute, reset };
}