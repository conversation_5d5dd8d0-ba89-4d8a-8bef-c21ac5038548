import { useState, useRef, useEffect, RefObject } from 'react';
import { logger } from '../../logging/Logger';

interface ViewportMargin {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

interface UseIsInViewportOptions {
  threshold?: number | number[];
  root?: Element | null;
  rootMargin?: string;
  margin?: ViewportMargin;
  thresholdPercentage?: number;
  rootMarginOptions?: ViewportMargin;
  trackVisibility?: boolean;
  delay?: number;
  callback?: (isInViewport: boolean, entry?: IntersectionObserverEntry) => void;
  debug?: boolean;
}

export function useIsInViewport<T extends HTMLElement = HTMLElement>(
  options: UseIsInViewportOptions = {}
): [RefObject<T>, boolean] {
  const [isInViewport, setIsInViewport] = useState(false);
  const elementRef = useRef<T>(null);

  const {
    threshold = 0,
    root = null,
    rootMargin = '0px',
    margin = { top: 0, right: 0, bottom: 0, left: 0 },
    thresholdPercentage = 0,
    rootMarginOptions = { top: 0, right: 0, bottom: 0, left: 0 },
    trackVisibility = false,
    delay = 0,
    callback,
    debug = false,
  } = options;

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const computedRootMargin = rootMargin ||
      `${rootMarginOptions.top}px ${rootMarginOptions.right}px ${rootMarginOptions.bottom}px ${rootMarginOptions.left}px`;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        const { isIntersecting, intersectionRect, boundingClientRect } = entry;

        let inViewport = isIntersecting;

        if (thresholdPercentage > 0) {
          const elementArea = boundingClientRect.width * boundingClientRect.height;
          const intersectionArea = intersectionRect.width * intersectionRect.height;
          const intersectionPercentage = elementArea > 0 ? (intersectionArea / elementArea) * 100 : 0;
          inViewport = intersectionPercentage >= thresholdPercentage;
        }

        if (margin.top || margin.right || margin.bottom || margin.left) {
          const rect = element.getBoundingClientRect();
          const windowHeight = window.innerHeight || document.documentElement.clientHeight;
          const windowWidth = window.innerWidth || document.documentElement.clientWidth;

          inViewport = (
            rect.top >= -margin.top &&
            rect.left >= -margin.left &&
            rect.bottom <= windowHeight + margin.bottom &&
            rect.right <= windowWidth + margin.right
          );
        }

        if (debug) {
          logger.debug('Viewport intersection:', {
            isIntersecting, inViewport, thresholdPercentage, element: element.tagName,
          });
        }

        setIsInViewport(inViewport);
        callback?.(inViewport, entry);
      },
      { threshold, root, rootMargin: computedRootMargin, trackVisibility, delay }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, root, rootMargin, margin, thresholdPercentage, rootMarginOptions, trackVisibility, delay, callback, debug]);

  return [elementRef, isInViewport];
}

export const viewportMargins = {
  none: { top: 0, right: 0, bottom: 0, left: 0 },
  small: { top: 10, right: 10, bottom: 10, left: 10 },
  medium: { top: 20, right: 20, bottom: 20, left: 20 },
  large: { top: 50, right: 50, bottom: 50, left: 50 },
};

export const viewportThresholds = {
  none: 0,
  quarter: 0.25,
  half: 0.5,
  threeQuarters: 0.75,
  full: 1.0,
};

export const viewportOptions = {
  default: { trackVisibility: false, delay: 0 },
  delayed: { trackVisibility: false, delay: 100 },
  tracked: { trackVisibility: true, delay: 0 },
  trackedAndDelayed: { trackVisibility: true, delay: 100 },
};