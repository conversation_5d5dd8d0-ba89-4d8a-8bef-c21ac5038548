import { RefObject, useEffect, useState, useCallback } from 'react';
import { logger } from '../../logging/Logger';

// --- useClickOutside ---

type ClickEvent = MouseEvent | TouchEvent;

export function useClickOutside<T extends HTMLElement = HTMLElement>(
  ref: RefObject<T>,
  handler: (event: ClickEvent) => void,
  mouseEvent: 'mousedown' | 'mouseup' = 'mousedown'
): void {
  useEffect(() => {
    const listener = (event: ClickEvent) => {
      const el = ref?.current;
      const target = event.target as Node;

      // Do nothing if clicking ref's element or descendent elements
      if (!el || el.contains(target)) {
        return;
      }

      handler(event);
    };

    document.addEventListener(mouseEvent, listener);
    document.addEventListener('touchstart', listener);

    return () => {
      document.removeEventListener(mouseEvent, listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, handler, mouseEvent]);
}

// --- useKeyPress & useHotkeys ---

type KeyFilter = string | string[] | ((event: KeyboardEvent) => boolean);

export function useKeyPress(
  keyFilter: KeyFilter,
  options: {
    target?: Window | Document | HTMLElement;
    event?: 'keydown' | 'keyup' | 'keypress';
    exact?: boolean;
  } = {}
): boolean {
  const [pressed, setPressed] = useState(false);

  useEffect(() => {
    const target = options.target || window;
    const eventName = options.event || 'keydown';

    const downHandler = (event: KeyboardEvent) => {
      if (typeof keyFilter === 'function') {
        if (keyFilter(event)) setPressed(true);
      } else {
        const keys = Array.isArray(keyFilter) ? keyFilter : [keyFilter];
        const isExact = options.exact || false;
        const keyMatch = isExact ? keys.includes(event.key) : keys.some(key => event.key.toLowerCase() === key.toLowerCase());
        if (keyMatch) setPressed(true);
      }
    };

    const upHandler = (event: KeyboardEvent) => {
      if (typeof keyFilter === 'function') {
        if (keyFilter(event)) setPressed(false);
      } else {
        const keys = Array.isArray(keyFilter) ? keyFilter : [keyFilter];
        const isExact = options.exact || false;
        const keyMatch = isExact ? keys.includes(event.key) : keys.some(key => event.key.toLowerCase() === key.toLowerCase());
        if (keyMatch) setPressed(false);
      }
    };

    target.addEventListener(eventName, downHandler as EventListener);
    target.addEventListener('keyup', upHandler as EventListener);

    return () => {
      target.removeEventListener(eventName, downHandler as EventListener);
      target.removeEventListener('keyup', upHandler as EventListener);
    };
  }, [keyFilter, options.target, options.event, options.exact]);

  return pressed;
}

export interface Hotkey {
  id: string;
  key: string;
  enabled: boolean;
  action: () => void;
  description?: string;
}

export function useHotkeys(hotkeys: Hotkey[] = []) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const keyCombination = [
      event.ctrlKey ? 'ctrl' : '',
      event.shiftKey ? 'shift' : '',
      event.altKey ? 'alt' : '',
      event.metaKey ? 'meta' : '',
      event.key.toLowerCase(),
    ]
      .filter(Boolean)
      .join('+');

    const matchingHotkey = hotkeys.find(
      hotkey => hotkey.enabled && hotkey.key.toLowerCase() === keyCombination
    );

    if (matchingHotkey) {
      event.preventDefault();
      matchingHotkey.action();
    }
  }, [hotkeys]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return {
    hotkeys,
    registerHotkey: (hotkey: Hotkey) => logger.info('Hotkey registered:', hotkey.id),
    unregisterHotkey: (id: string) => logger.info('Hotkey unregistered:', id),
  };
}