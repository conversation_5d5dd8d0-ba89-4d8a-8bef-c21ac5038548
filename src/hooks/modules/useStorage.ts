import { useState, useCallback, useEffect } from 'react';
import { errorManager } from '../../core/ErrorManager';

export interface UseStorageOptions {
  serializer?: {
    parse: (value: string) => any;
    stringify: (value: any) => string;
  };
  syncAcrossTabs?: boolean;
  onError?: (error: Error) => void;
}

function useWebStorage<T>(
  storage: Storage,
  key: string,
  defaultValue: T,
  options: UseStorageOptions = {}
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  const {
    serializer = JSON,
    syncAcrossTabs = storage === window.localStorage,
    onError = (error) => errorManager.handleError(error, 'error', 'storage'),
  } = options;

  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window === 'undefined') return defaultValue;
      
      const item = storage.getItem(key);
      return item ? serializer.parse(item) : defaultValue;
    } catch (error) {
      onError(error as Error);
      return defaultValue;
    }
  });

  const setValue = useCallback(
    (value: T | ((prev: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        
        if (typeof window !== 'undefined') {
          storage.setItem(key, serializer.stringify(valueToStore));
        }
      } catch (error) {
        onError(error as Error);
      }
    },
    [key, storedValue, serializer, onError, storage]
  );

  const removeValue = useCallback(() => {
    try {
      setStoredValue(defaultValue);
      if (typeof window !== 'undefined') {
        storage.removeItem(key);
      }
    } catch (error) {
      onError(error as Error);
    }
  }, [key, defaultValue, onError, storage]);

  // Sync across tabs (for localStorage)
  useEffect(() => {
    if (!syncAcrossTabs || typeof window === 'undefined' || storage !== window.localStorage) return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(serializer.parse(e.newValue));
        } catch (error) {
          onError(error as Error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, serializer, onError, syncAcrossTabs, storage]);

  return [storedValue, setValue, removeValue];
}

export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  options: UseStorageOptions = {}
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  if (typeof window === 'undefined') {
    const noop = () => {};
    return [defaultValue, noop, noop];
  }
  return useWebStorage(window.localStorage, key, defaultValue, options);
}

export function useSessionStorage<T>(
  key: string,
  defaultValue: T,
  options: UseStorageOptions = {}
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  if (typeof window === 'undefined') {
    const noop = () => {};
    return [defaultValue, noop, noop];
  }
  return useWebStorage(window.sessionStorage, key, defaultValue, options);
}