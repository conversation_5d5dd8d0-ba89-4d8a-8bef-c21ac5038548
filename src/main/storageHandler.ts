import { ipcMain } from 'electron';
import Store from 'electron-store';
import { compress, decompress } from 'lz-string';

const store = new Store();

export function setupStorageHandlers() {
  ipcMain.handle('storage:getItem', (event, key: string) => {
    try {
      const value = store.get(key) as string | undefined;
      return value ? decompress(value) : null;
    } catch (e) {
      console.error(`Failed to get item from store for key: ${key}`, e);
      return null;
    }
  });

  ipcMain.handle('storage:setItem', (event, key: string, value: string) => {
    try {
      const compressed = compress(value);
      store.set(key, compressed);
    } catch (e) {
      console.error(`Failed to set item in store for key: ${key}`, e);
    }
  });

  ipcMain.handle('storage:removeItem', (event, key: string) => {
    store.delete(key);
  });

  ipcMain.handle('storage:clear', () => {
    store.clear();
  });

  ipcMain.handle('storage:getAllKeys', () => {
    return Object.keys(store.store);
  });
}