/**
 * Unified Providers
 * Consolidates all React providers and contexts into a single comprehensive system
 */

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { Provider as ReduxProvider } from 'react-redux';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Backdrop, CircularProgress, Alert, AlertColor, Snackbar } from '@mui/material';
import { compress, decompress } from 'lz-string';

import { store } from '../store';
import { logger } from '../logging/Logger';

// ============================================================================
// UNIFIED THEME PROVIDER (consolidates both ThemeProvider implementations)
// ============================================================================

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

interface UnifiedTheme {
  mode: ThemeMode;
  colors: ThemeColors;
  fontFamily: string;
  fontSize: {
    small: string;
    medium: string;
    large: string;
  };
  spacing: {
    small: string;
    medium: string;
    large: string;
  };
  borderRadius: string;
  transition: string;
}

interface ThemeContextType {
  theme: UnifiedTheme;
  setThemeMode: (mode: ThemeMode) => Promise<void>;
  updateThemeColors: (colors: Partial<ThemeColors>) => Promise<void>;
  updateThemeFont: (fontFamily: string) => Promise<void>;
  resetTheme: () => Promise<void>;
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
}

const defaultTheme: UnifiedTheme = {
  mode: 'system',
  colors: {
    primary: '#2196f3',
    secondary: '#f50057',
    background: '#ffffff',
    surface: '#f5f5f5',
    text: '#000000',
    textSecondary: '#666666',
    border: '#e0e0e0',
    error: '#f44336',
    warning: '#ff9800',
    success: '#4caf50',
    info: '#2196f3',
  },
  fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  fontSize: {
    small: '0.875rem',
    medium: '1rem',
    large: '1.25rem',
  },
  spacing: {
    small: '0.5rem',
    medium: '1rem',
    large: '2rem',
  },
  borderRadius: '4px',
  transition: 'all 0.3s ease',
};

const ThemeContext = createContext<ThemeContextType | null>(null);

export const useUnifiedTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useUnifiedTheme must be used within a UnifiedThemeProvider');
  }
  return context;
};

// ============================================================================
// UNIFIED NOTIFICATION PROVIDER (consolidates notification systems)
// ============================================================================

interface Notification {
  id: string;
  message: string;
  type: AlertColor;
  duration?: number;
  timestamp: number;
}

interface NotificationContextType {
  notifications: Notification[];
  showNotification: (message: string, type: AlertColor, duration?: number) => void;
  hideNotification: (id: string) => void;
  clearAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | null>(null);

export const useUnifiedNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useUnifiedNotification must be used within a UnifiedNotificationProvider');
  }
  return context;
};

// ============================================================================
// UNIFIED LOADING PROVIDER (consolidates loading systems)
// ============================================================================

interface LoadingContextType {
  isLoading: boolean;
  loadingCount: number;
  loadingMessage: string;
  showLoading: (message?: string) => void;
  hideLoading: () => void;
  setLoadingMessage: (message: string) => void;
}

const LoadingContext = createContext<LoadingContextType | null>(null);

export const useUnifiedLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useUnifiedLoading must be used within a UnifiedLoadingProvider');
  }
  return context;
};

// ============================================================================
// UNIFIED STORAGE PROVIDER
// ============================================================================

interface StorageContextType {
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: string) => Promise<void>;
  removeItem: (key: string) => Promise<void>;
  clear: () => Promise<void>;
  getAllKeys: () => Promise<string[]>;
  getSize: () => Promise<number>;
}

const StorageContext = createContext<StorageContextType | null>(null);

export const useUnifiedStorage = () => {
  const context = useContext(StorageContext);
  if (!context) {
    throw new Error('useUnifiedStorage must be used within a UnifiedStorageProvider');
  }
  return context;
};

// ============================================================================
// UNIFIED PROVIDERS COMPONENT
// ============================================================================

interface UnifiedProvidersProps {
  children: ReactNode;
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

export const UnifiedProviders: React.FC<UnifiedProvidersProps> = ({ children }) => {
  // Theme state
  const [theme, setTheme] = useState<UnifiedTheme>(defaultTheme);
  
  // Notification state
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [currentNotification, setCurrentNotification] = useState<Notification | null>(null);
  
  // Loading state
  const [loadingCount, setLoadingCount] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState('');

  // Storage implementation using IPC for Electron robustness
  const storage: StorageContextType = useMemo(() => ({
    getItem: async (key: string) => {
      try {
        // In a real Electron app, this would be exposed via a preload script
        const compressedValue = await window.electron.storage.getItem(key);
        return compressedValue ? decompress(compressedValue) : null;
      } catch (error) {
        logger.error('Storage getItem error:', error);
        return null;
      }
    },
    setItem: async (key: string, value: string) => {
      try {
        const compressedValue = compress(value);
        await window.electron.storage.setItem(key, compressedValue);
      } catch (error) {
        logger.error('Storage setItem error:', error);
      }
    },
    removeItem: async (key: string) => {
      try {
        await window.electron.storage.removeItem(key);
      } catch (error) {
        logger.error('Storage removeItem error:', error);
      }
    },
    clear: async () => {
      try {
        await window.electron.storage.clear();
      } catch (error) {
        logger.error('Storage clear error:', error);
      }
    },
    getAllKeys: async () => {
      try {
        return await window.electron.storage.getAllKeys();
      } catch (error) {
        logger.error('Storage getAllKeys error:', error);
        return [];
      }
    },
    getSize: async () => {
      // This might be less efficient over IPC, consider if needed
      try {
        const keys = await window.electron.storage.getAllKeys();
        return keys.length;
      } catch (error) {
        logger.error('Storage getSize error:', error);
        return 0;
      }
    },
  }), []);

  // Theme methods
  const setThemeMode = useCallback(async (mode: ThemeMode) => {
    const newTheme = { ...theme, mode };
    setTheme(newTheme);
    await storage.setItem('theme', JSON.stringify(newTheme));
  }, [theme]);

  const updateThemeColors = useCallback(async (colors: Partial<ThemeColors>) => {
    const newTheme = { ...theme, colors: { ...theme.colors, ...colors } };
    setTheme(newTheme);
    await storage.setItem('theme', JSON.stringify(newTheme));
  }, [theme]);

  const updateThemeFont = useCallback(async (fontFamily: string) => {
    const newTheme = { ...theme, fontFamily };
    setTheme(newTheme);
    await storage.setItem('theme', JSON.stringify(newTheme));
  }, [theme]);

  const resetTheme = useCallback(async () => {
    setTheme(defaultTheme);
    await storage.setItem('theme', JSON.stringify(defaultTheme));
  }, []);

  // Notification methods
  const showNotification = useCallback((message: string, type: AlertColor, duration = 4000) => {
    const id = Date.now().toString();
    const notification: Notification = {
      id,
      message,
      type,
      duration,
      timestamp: Date.now(),
    };
    
    setNotifications(prev => [...prev, notification]);
  }, []);

  const hideNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Loading methods
  const showLoading = useCallback((message = 'Loading...') => {
    setLoadingCount(prev => prev + 1);
    setLoadingMessage(message);
  }, []);

  const hideLoading = useCallback(() => {
    setLoadingCount(prev => Math.max(0, prev - 1));
  }, []);

  // Load saved theme on mount
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await storage.getItem('theme');
        if (savedTheme) {
          setTheme(JSON.parse(savedTheme));
        }
      } catch (error) {
        logger.error('Error loading theme:', error);
      }
    };
    loadTheme();
  }, []);

  // Effect for auto-hiding notifications
  // This effect manages the queue of notifications to be displayed one by one.
  useEffect(() => {
    if (notifications.length > 0 && !currentNotification) {
      const nextNotification = notifications[0];
      setCurrentNotification(nextNotification);
    } else if (notifications.length === 0 && currentNotification) {
      // If all notifications were cleared, close the current one.
      setCurrentNotification(null);
    }
  }, [notifications, currentNotification]);

  const handleSnackbarClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    // The onClose will trigger hiding the current notification, and the useEffect will pick the next one.
    if (currentNotification) hideNotification(currentNotification.id);
    setCurrentNotification(null);
  };

  const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const finalThemeMode = theme.mode === 'system' ? (isSystemDark ? 'dark' : 'light') : theme.mode;

  // Create MUI theme
  const muiTheme = createTheme({
    palette: {
      mode: finalThemeMode,
      primary: {
        main: theme.colors.primary,
      },
      secondary: {
        main: theme.colors.secondary,
      },
      background: {
        default: theme.colors.background,
        paper: theme.colors.surface,
      },
      text: {
        primary: theme.colors.text,
        secondary: theme.colors.textSecondary,
      },
    },
    typography: {
      fontFamily: theme.fontFamily,
    },
    transitions: {
      duration: {
        standard: 300,
      },
    },
  });

  // Effect for handling system theme changes
  useEffect(() => {
    if (theme.mode !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = () => {
      // Force a re-render by updating the state with the same object
      setTheme(t => ({...t}));
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [theme.mode]);

  const themeValue: ThemeContextType = {
    theme,
    setThemeMode,
    updateThemeColors,
    updateThemeFont,
    resetTheme,
    isDark: finalThemeMode === 'dark',
    isLight: finalThemeMode === 'light',
    isSystem: theme.mode === 'system'
  };

  const notificationValue: NotificationContextType = {
    notifications,
    showNotification,
    hideNotification,
    clearAllNotifications,
  };

  const loadingValue: LoadingContextType = {
    isLoading: loadingCount > 0,
    loadingCount,
    loadingMessage,
    showLoading,
    hideLoading,
    setLoadingMessage,
  };

  return (
    <ReduxProvider store={store}>
      <QueryClientProvider client={queryClient}>
        <StorageContext.Provider value={storage}>
          <ThemeContext.Provider value={themeValue}>
            <MuiThemeProvider theme={muiTheme}>
              <CssBaseline />
              <NotificationContext.Provider value={notificationValue}>
                <LoadingContext.Provider value={loadingValue}>
                  {children}
                  
                  {/* Loading Backdrop */}
                  <Backdrop
                    sx={{ color: '#fff', zIndex: theme => theme.zIndex.drawer + 1 }}
                    open={loadingCount > 0}
                  >
                    <CircularProgress color="inherit" />
                    {loadingMessage && (
                      <div style={{ marginLeft: 16 }}>{loadingMessage}</div>
                    )}
                  </Backdrop>

                  {/* Notifications */}
                  <Snackbar
                    open={!!currentNotification}
                    autoHideDuration={currentNotification?.duration}
                    onClose={handleSnackbarClose}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                  >
                    <Alert
                      onClose={handleSnackbarClose}
                      severity={currentNotification?.type || 'info'}
                      variant="filled"
                      sx={{ width: '100%' }}
                    >
                      {currentNotification?.message}
                    </Alert>
                  </Snackbar>
                </LoadingContext.Provider>
              </NotificationContext.Provider>
            </MuiThemeProvider>
          </ThemeContext.Provider>
        </StorageContext.Provider>
      </QueryClientProvider>
    </ReduxProvider>
  );
};

// Legacy exports for backward compatibility
export const useTheme = useUnifiedTheme;
export const useNotification = useUnifiedNotification;
export const useLoading = useUnifiedLoading;
export const useStorage = useUnifiedStorage;
