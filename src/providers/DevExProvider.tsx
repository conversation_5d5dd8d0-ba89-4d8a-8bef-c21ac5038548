import React, { createContext, useContext, useMemo, useState, useCallback, useEffect } from 'react';
import { DevExManager, DevExConfig } from '../developer-experience/DevExManager';
import { settingsPersistence } from '../developer-experience/SettingsPersistence';
import { AllUserRulesConfig, UserRuleConfig } from '../developer-experience/recommendations/RecommendationEngine';

interface IDevExContext {
  manager: DevExManager;
  config: DevExConfig;
  updateRecommendationRuleConfig: (ruleId: string, newConfig: UserRuleConfig) => void;
  resetRecommendationRulesToDefault: () => void;
}

const DevExContext = createContext<IDevExContext | null>(null);

/**
 * A custom hook to access the DevExManager instance and related functions.
 * Must be used within a DevExProvider.
 */
export const useDevEx = (): IDevExContext => {
  const context = useContext(DevExContext);
  if (!context) {
    throw new Error('useDevEx must be used within a DevExProvider');
  }
  return context;
};

interface DevExProviderProps {
  children: React.ReactNode;
}

/**
 * This provider is responsible for instantiating and managing the lifecycle
 * of the DevExManager. It handles loading and persisting settings and provides
 * the manager instance to the rest of the application via context.
 */
export const DevExProvider: React.FC<DevExProviderProps> = ({ children }) => {
  const [config, setConfig] = useState<DevExConfig | null>(null);

  // Load initial config from persistence only once on mount.
  useEffect(() => {
    const persistedConfig = settingsPersistence.load() ?? {};
    const initialConfig: DevExConfig = {
      enableHotReload: true,
      enableTypeChecking: true,
      enableLinting: true,
      enableFormatting: true,
      enableDebugging: true,
      enableProfiling: true,
      enableCodeGeneration: true,
      enableSnippets: true,
      enableIntelliSense: true,
      enableErrorBoundaries: true,
      ...persistedConfig,
      // Deep merge recommendations to ensure defaults are not lost
      recommendations: {
        ...(persistedConfig.recommendations ?? {}),
      },
    };
    setConfig(initialConfig);
  }, []);

  const manager = useMemo(() => {
    if (!config) return null;
    return new DevExManager(config);
  }, [config]);

  const updateRecommendationRuleConfig = useCallback((ruleId: string, newConfig: UserRuleConfig) => {
    setConfig(prevConfig => {
      if (!prevConfig) return null;
      const newRecommendations: AllUserRulesConfig = {
        ...(prevConfig.recommendations ?? {}),
        [ruleId]: { ...(prevConfig.recommendations?.[ruleId] ?? {}), ...newConfig },
      };
      const updatedConfig = { ...prevConfig, recommendations: newRecommendations };
      settingsPersistence.save({ recommendations: newRecommendations });
      return updatedConfig;
    });
  }, []);

  const resetRecommendationRulesToDefault = useCallback(() => {
    setConfig(prevConfig => {
      if (!prevConfig) return null;
      const updatedConfig = { ...prevConfig, recommendations: {} };
      settingsPersistence.save({ recommendations: {} });
      return updatedConfig;
    });
  }, []);

  if (!manager || !config) {
    // You can return a loading spinner here if needed
    return null;
  }

  const value: IDevExContext = { manager, config, updateRecommendationRuleConfig, resetRecommendationRulesToDefault };

  return <DevExContext.Provider value={value}>{children}</DevExContext.Provider>;
};