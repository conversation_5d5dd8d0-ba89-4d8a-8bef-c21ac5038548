import { Al<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Button, Typography } from '@mui/material';
import React, { useState } from 'react';
import { FallbackProps } from 'react-error-boundary';
import { useTranslation } from 'react-i18next';

export const ErrorFallback: React.FC<FallbackProps> = ({ error, resetErrorBoundary }) => {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleGoBack = () => {
    // Пытаемся вернуться назад. После этого сбрасываем ошибку,
    // чтобы пользователь мог взаимодействовать со страницей.
    if (window.history.length > 1) {
      window.history.back();
    }
    resetErrorBoundary();
  };

  const handleSendReport = () => {
    setIsSubmitting(true);
    // Имитация асинхронной отправки отчета
    // В реальном приложении здесь будет вызов API вашего сервиса мониторинга
    // monitoringService.sendReport({ error, componentStack: '...' });
    console.log('Sending error report:', {
      message: error.message,
      stack: error.stack,
    });

    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 1500);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        p: 3,
        textAlign: 'center',
      }}
    >
      <Typography variant="h4" gutterBottom>
        {t('errorBoundary.title')}
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        {t('errorBoundary.message')}
      </Typography>
      {process.env.NODE_ENV === 'development' && error && (
        <Alert severity="error" sx={{ mt: 2, textAlign: 'left', maxWidth: '80vw' }}>
          <AlertTitle>{t('errorBoundary.details')}</AlertTitle>
          <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all', marginTop: '8px', fontSize: '12px' }}>
            <code>
              {error.message}
              {error.stack && `\n\n${error.stack}`}
            </code>
          </pre>
        </Alert>
      )}
      <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
        <Button variant="outlined" color="secondary" onClick={handleGoBack}>
          {t('errorBoundary.goBack')}
        </Button>
        <Button variant="contained" color="primary" onClick={resetErrorBoundary}>
          {t('errorBoundary.tryAgain')}
        </Button>
        <Button
          variant="contained"
          color="secondary"
          onClick={handleSendReport}
          disabled={isSubmitting || isSubmitted}
        >
          {isSubmitting
            ? t('errorBoundary.sendingReport')
            : isSubmitted
            ? t('errorBoundary.reportSent')
            : t('errorBoundary.sendReport')}
        </Button>
      </Box>
    </Box>
  );
};
