import React from 'react';
import { createRoot } from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ErrorBoundary } from 'react-error-boundary';
import { CssBaseline, ThemeProvider } from '@mui/material';

import App from './App';
import './index.css';
import '../i18n/config'; // Инициализация i18next
import { ErrorFallback } from './components/ErrorBoundary';
import theme from './theme';

// Create a client for TanStack Query
const queryClient = new QueryClient();

const container = document.getElementById('root');
const root = createRoot(container!);

root.render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <QueryClientProvider client={queryClient}>
        <ErrorBoundary
          FallbackComponent={ErrorFallback}
          onError={(error, info) => {
            // В будущем здесь будет вызов вашего унифицированного логгера
            // logger.error('Uncaught React error', { error, componentStack: info.componentStack });
            console.error('Caught by ErrorBoundary:', error, info);
          }}
        >
          <App />
        </ErrorBoundary>
      </QueryClientProvider>
    </ThemeProvider>
  </React.StrictMode>
);
