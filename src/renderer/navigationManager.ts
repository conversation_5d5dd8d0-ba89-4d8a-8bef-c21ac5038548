/**
 * @file navigationManager.ts
 * @description Handles browser navigation controls like back, forward, reload, and URL input.
 */

import { useNavigationStore } from './stores/useNavigationStore.ts';
import { useTabStore } from './stores/useTabStore.ts';
import { useSettingsStore } from './stores/useSettingsStore.ts'; // Добавлен недостающий импорт

// Определяем интерфейсы для зависимостей и данных
interface Tab {
  id: string;
  isLoading: boolean;
  webview: Electron.WebviewTag;
  goBack: () => void;
  goForward: () => void;
  reload: () => void;
  stopLoading: () => void;
  navigateTo: (url: string) => void;
}

interface TabManager {
  getActiveTab: () => Tab | null;
}

let isInitialized = false;

/**
 * @description Initializes navigation controls and event listeners.
 */
function initializeNavigation(): void {
  if (isInitialized) return;

  // Обновляем состояние кнопок при смене вкладки или загрузке страницы
  // Подписываемся на изменения активной вкладки и ее состояния загрузки
  useTabStore.subscribe(
    (state) => {
      updateNavigationState(state.activeTabId);
    }
  );

  // Первоначальное обновление состояния
  updateNavigationState();
  isInitialized = true;
  console.log('NavigationManager initialized.');
}

/**
 * @description Updates the state of navigation buttons (enabled/disabled) based on the active tab's state.
 * Логика теперь полностью полагается на состояние из `useTabStore` и `useNavigationStore`.
 * @param {string | null} [activeTabId] - The ID of the currently active tab.
 */
function updateNavigationState(activeTabId?: string | null): void {
  const id = activeTabId ?? useTabStore.getState().activeTabId;
  const setStoreState = useNavigationStore.getState().setNavigationState;
  
  if (!id) {
    // Если нет активной вкладки, сбрасываем состояние
    setStoreState({
      canGoBack: false,
      canGoForward: false,
      isLoading: false,
      url: '',
    });
    return;
  }

  const activeTab = useTabStore.getState().tabs.find(t => t.id === id);
  if (activeTab) {
    setStoreState(activeTab.navigationState);
  } else {
    // Если нет активной вкладки
    setStoreState({
      canGoBack: false,
      canGoForward: false,
      isLoading: false,
      url: '',
    });
  }
}

/**
 * @description Navigates back in the active tab's history.
 * Delegates the action to the tab store.
 */
function goBack(): void {
  useTabStore.getState().goBack();
}

/**
 * @description Navigates forward in the active tab's history.
 * Delegates the action to the tab store.
 */
function goForward(): void {
  useTabStore.getState().goForward();
}

/**
 * @description Reloads the active tab or stops its loading.
 * Delegates the action to the tab store.
 */
function reloadOrStop(): void {
  useTabStore.getState().reloadOrStop();
}

/**
 * @description Navigates the active tab to a new URL.
 * Handles URL parsing and search engine queries.
 * @param {string} url - The URL or search query to navigate to.
 */
function navigate(url: string): void {
  if (!url || typeof url !== 'string') return;

  let targetUrl = url;
  const { searchEngineUrl } = useSettingsStore.getState();
  const defaultSearchEngine = 'https://www.google.com/search?q=%s';

  try {
    // Check if it's a full URL
    // eslint-disable-next-line no-new
    new URL(url);
  } catch (e) {
    // If not a full URL, check if it's a valid hostname (e.g., "example.com")
    // or localhost. A simple check for a dot is a good heuristic.
    const isHostname = url.includes('.') || url.startsWith('localhost:');
    if (isHostname) {
      targetUrl = `http://${url}`;
    } else {
      // Otherwise, treat it as a search query
      const engine = searchEngineUrl || defaultSearchEngine;
      targetUrl = engine.replace('%s', encodeURIComponent(url));
    }
  }

  useTabStore.getState().navigateTo(targetUrl);
}

// Экспорт функций (если они будут использоваться другими модулями)
export default {
  initializeNavigation,
  updateNavigationState,
  // Новые методы для вызова из UI
  goBack,
  goForward,
  reloadOrStop,
  navigate,
};
