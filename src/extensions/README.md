# Extension API Documentation

## Overview
A14 Browser provides a secure extension system where each extension runs in a fully isolated sandbox (`iframe`). Communication with the browser is handled securely via a proxied API (`window.browser` or `window.chrome`) that uses `postMessage` under the hood.

## Extension Manifest

Every extension must have a `manifest.json` file in its root directory, which defines its metadata and required permissions.

```javascript
// manifest.json
{
  "manifest_version": 3,
  "name": "My Awesome Extension",
  "version": "1.0.0",
  "permissions": ["tabs", "storage", "notifications"],
  "background": {
    "service_worker": "background.js"
  }
}
```

## Available APIs
- **tabs**: Manage browser tabs
- **storage**: Persistent data storage
- **notifications**: Display system notifications
- **network**: Intercept network requests

## Example Extensions
See implementations in `src/extensions/examples/`:
- Dark Mode Toggle
- Tab Groups
- AdBlocker Integration

## Best Practices
1. Use limited capabilities
2. Validate all user input
3. Handle errors gracefully
4. Test in sandbox mode

## Security Considerations
- All extensions run in isolated context
- Limited system access via capability model
- Automatic updates from trusted sources

## Contribution Guidelines
- Follow JavaScript Standard Style
- Include unit tests
- Document public API methods