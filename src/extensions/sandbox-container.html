<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Extension Sandbox</title>
  <style>
    /* Стили для контейнера, если необходимо */
    body {
      margin: 0;
      padding: 0;
      /* Можно добавить стили для отладки, например, рамку */
      /* border: 1px solid red; */
    }
  </style>
</head>
<body>
  <script>
    let extensionId = null;
    let parentOrigin = null; // Для безопасного postMessage
    let extensionAPIProxy = {};
    let callIdCounter = 0;
    const pendingApiCalls = new Map();

    // Централизованная система обработки событий
    const eventListeners = new Map();

    function createEventProxy(eventName) {
      if (!eventListeners.has(eventName)) {
        eventListeners.set(eventName, new Set());
      }
      const listeners = eventListeners.get(eventName);
      return {
        addListener: (callback) => listeners.add(callback),
        removeListener: (callback) => listeners.delete(callback),
      };
    }

    // Функция для отправки вызовов API в ExtensionManager
    function callHostAPI(namespace, method, ...args) {
      return new Promise((resolve, reject) => {
        if (!parentOrigin) {
          // Нельзя вызывать API до инициализации
          return reject(new Error('Sandbox not initialized. Cannot call API.'));
        }

        const callId = callIdCounter++;
        pendingApiCalls.set(callId, { resolve, reject });
        window.parent.postMessage({
          type: 'apiCall',
          extensionId: extensionId,
          callDetails: {
            callId: callId,
            apiNamespace: namespace,
            methodName: method,
            args: args
          }
        }, parentOrigin); // Используем конкретный origin для безопасности
      });
    }

    // Создание прокси для API расширения
    function createAPIProxy(grantedPermissions = []) {
      const proxy = {};
      
      proxy.runtime = {
        getManifest: () => callHostAPI('runtime', 'getManifest'),
        onMessage: createEventProxy('runtime.onMessage'),
        // другие методы runtime API
      };

      // Пример для 'tabs'
      if (grantedPermissions.includes('tabs') || grantedPermissions.includes('activeTab')) {
        proxy.tabs = {
          query: (queryInfo) => callHostAPI('tabs', 'query', queryInfo),
          create: (createProperties) => callHostAPI('tabs', 'create', createProperties),
          update: (tabId, updateProperties) => callHostAPI('tabs', 'update', tabId, updateProperties),
          // ... другие методы tabs API
        };
      }

      // Пример для 'storage'
      if (grantedPermissions.includes('storage')) {
        proxy.storage = {
          local: {
            get: (keys) => callHostAPI('storage', 'get', keys),
            set: (items) => callHostAPI('storage', 'set', items),
            remove: (keys) => callHostAPI('storage', 'remove', keys),
            clear: () => callHostAPI('storage', 'clear'),
          }
          // sync, managed storage и т.д.
        };
      }
      
      // Пример для 'notifications'
      if (grantedPermissions.includes('notifications')) {
        proxy.notifications = {
          create: (notificationId, options) => callHostAPI('notifications', 'create', notificationId, options),
          clear: (notificationId) => callHostAPI('notifications', 'clear', notificationId),
        };
      }

      // API для разрешений
      proxy.permissions = {
        onAdded: createEventProxy('permissions.onAdded'),
        onRemoved: createEventProxy('permissions.onRemoved'),
        contains: (permissionsToCheck) => callHostAPI('permissions', 'contains', permissionsToCheck),
        request: (permissionsToRequest) => callHostAPI('permissions', 'request', permissionsToRequest),
      };

      return proxy;
    }

    window.addEventListener('message', async (event) => {
      const { data, source } = event;

      // Инициализация - это единственное сообщение, которое мы принимаем до установки parentOrigin.
      // После инициализации мы будем принимать сообщения только от доверенного parentOrigin.
      if (data && data.type === 'initialize') {
        // Устанавливаем доверенный origin, который будет использоваться для всех последующих коммуникаций.
        parentOrigin = event.origin;
        extensionId = data.extensionId;
        console.log(`[Sandbox ${extensionId}] Инициализация от origin: ${parentOrigin}`);
        
        const grantedPermissions = data.grantedPermissions || []; 
        window.chrome = window.browser = createAPIProxy(grantedPermissions);

      } else if (event.origin !== parentOrigin) {
        // Игнорируем все остальные сообщения от недоверенных источников
        return;
      }

      if (data.type === 'initialize' && data.scriptContent) {
        try {
          console.log(`[Sandbox ${extensionId}] Загрузка скрипта расширения...`);
          const scriptElement = document.createElement('script');
          
          // Создаем безопасный Blob URL из содержимого скрипта.
          const scriptBlob = new Blob([data.scriptContent], { type: 'text/javascript' });
          scriptElement.src = URL.createObjectURL(scriptBlob);

          scriptElement.onload = () => { console.log(`[Sandbox ${extensionId}] Скрипт загружен и выполнен.`); URL.revokeObjectURL(scriptElement.src); };
          scriptElement.onerror = (e) => {
              console.error(`[Sandbox ${extensionId}] Ошибка загрузки или выполнения скрипта:`, e);
              window.parent.postMessage({ type: 'scriptError', extensionId, error: `Ошибка загрузки скрипта` }, parentOrigin);
          };
          document.body.appendChild(scriptElement);

        } catch (e) {
          console.error(`[Sandbox ${extensionId}] Ошибка при настройке загрузки скрипта расширения:`, e);
          window.parent.postMessage({ type: 'scriptError', extensionId, error: e.message }, parentOrigin);
        }
      } else if (data && data.type === 'apiResponse') {
        const { callId, result, error } = data;
        if (pendingApiCalls.has(callId)) {
          const { resolve, reject } = pendingApiCalls.get(callId);
          if (error) {
            // Воссоздаем объект Error для сохранения стека вызовов
            const err = new Error(error.message);
            err.name = error.name || 'HostError';
            err.stack = error.stack;
            reject(err);
          } else {
            resolve(result);
          }
          pendingApiCalls.delete(callId);
        }
      } else if (data && data.type === 'dispatchExtensionEvent') {
        const { eventName, args } = data;
        if (eventListeners.has(eventName)) {
          console.log(`[Sandbox ${extensionId}] Диспетчеризация события: ${eventName}`, args);
          const listeners = eventListeners.get(eventName);
          listeners.forEach(listener => {
            try {
              // args - это массив, его нужно распаковать
              listener(...args);
            } catch (e) {
              console.error(`[Sandbox ${extensionId}] Ошибка в обработчике события ${eventName}:`, e);
            }
          });
        }
      }
    });

    // Сообщаем родительскому окну, что песочница готова к инициализации.
    // Отправляем с wildcard, так как на этом этапе мы еще не знаем origin родителя.
    if (window.parent && window.parent !== window) {
        window.parent.postMessage({ type: 'sandboxReady' }, '*');
    }
  </script>
</body>
</html>