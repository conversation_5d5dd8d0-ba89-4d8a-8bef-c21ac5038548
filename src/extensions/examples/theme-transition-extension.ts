/**
 * @file theme-enhancer-extension.ts
 * @description Образцовое расширение для A14 Browser, которое показывает уведомление при смене темы.
 * @version 3.0.0
 *
 * CHANGELOG:
 * - v3.0.0: Переписано на TypeScript для соответствия стандартам проекта.
 *           - Добавлены типы для API и сообщений.
 * - v2.0.0: Полный рефакторинг для соответствия лучшим практикам.
 *           - Убраны все прямые манипуляции с DOM.
 *           - Используется API `browser.runtime.onMessage` для получения событий.
 *           - Используется API `browser.notifications.create` для показа уведомлений.
 *           - Расширение теперь работает как фоновый скрипт, не вмешиваясь в UI браузера.
 */

// Определяем типы для API, чтобы получить автодополнение и проверку типов.
// В реальном проекте эти типы могут быть предоставлены в виде @types/a14-browser.
interface BrowserNotificationOptions {
  type: 'basic' | 'image' | 'list' | 'progress';
  title: string;
  message: string;
  iconUrl?: string;
}

interface Theme {
  mode: 'dark' | 'light';
  // ... другие свойства темы
}

interface ThemeChangedMessage {
  type: 'A14_THEME_CHANGED';
  payload: {
    newTheme: Theme;
  };
}

interface BrowserAPI {
  runtime: {
    onMessage: {
      addListener: (callback: (message: ThemeChangedMessage, sender: any) => void) => void;
    };
  };
  notifications: {
    create: (id: string, options: BrowserNotificationOptions) => Promise<string>;
  };
}

declare global {
  interface Window {
    browser: BrowserAPI;
  }
}

const config = {
  id: 'theme-enhancer-extension',
  name: 'Theme Enhancer',
  version: '3.0.0',
};

console.log(`[${config.name}] Инициализация расширения v${config.version}...`);

window.browser.runtime.onMessage.addListener((message: ThemeChangedMessage) => {
  if (message?.type === 'A14_THEME_CHANGED' && message.payload?.newTheme) {
    const { newTheme } = message.payload;
    console.log(`[${config.name}] Получено событие смены темы:`, newTheme.mode);

    window.browser.notifications.create(`theme-change-${Date.now()}`, {
      type: 'basic',
      title: 'Тема изменена',
      message: `Браузер переключился на ${newTheme.mode === 'dark' ? 'темную' : 'светлую'} тему.`,
      // iconUrl: 'icons/icon48.png' // Путь к иконке внутри расширения
    });
  }
});

console.log(`[${config.name}] Расширение готово и ожидает событий смены темы.`);