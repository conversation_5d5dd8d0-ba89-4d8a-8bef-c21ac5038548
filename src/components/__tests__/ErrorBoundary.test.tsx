import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import React from 'react';

import { ErrorBoundary } from '../ErrorBoundary';

function ThrowComponent() {
  throw new Error('Crash!');
}

describe('ErrorBoundary', () => {
  // Подавляем ожидаемый вывод ошибки в консоль во время тестов,
  // чтобы не засорять лог выполнения тестов.
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    // Восстанавливаем оригинальную функцию console.error после каждого теста.
    vi.restoreAllMocks();
  });

  it('должен показывать fallback при ошибке', () => {
    // Рендерим компонент, который гарантированно вызовет ошибку
    render(<ErrorBoundary FallbackComponent={() => <div data-testid="error-fallback">Ошибка!</div>}><ThrowComponent /></ErrorBoundary>);
    
    expect(screen.getByTestId('error-fallback')).toBeInTheDocument();
  });

  it('не должен показывать fallback без ошибки', () => {
    render(
      <ErrorBoundary FallbackComponent={() => <div>Ошибка!</div>}>
        <div data-testid="inside">Всё нормально</div>
      </ErrorBoundary>
    );
    expect(screen.getByTestId('inside')).toBeInTheDocument();
  });
});
