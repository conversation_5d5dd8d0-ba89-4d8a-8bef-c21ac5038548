import React from 'react';
import { FormControlLabel, Switch, SwitchProps, Typography, Box } from '@mui/material';

interface FormSwitchProps extends Omit<SwitchProps, 'onChange'> {
  label: React.ReactNode;
  onChange: (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => void;
}

export const FormSwitch: React.FC<FormSwitchProps> = React.memo(({ label, ...switchProps }) => (
  <FormControlLabel
    control={<Switch {...switchProps} />}
    label={<Typography variant="body1">{label}</Typography>}
    sx={{ justifyContent: 'space-between', ml: 0, width: '100%' }}
  />
));