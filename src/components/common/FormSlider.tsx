import React from 'react';
import { Box, Slider, SliderProps, Typography } from '@mui/material';

interface FormSliderProps extends SliderProps {
  label: React.ReactNode;
}

export const FormSlider: React.FC<FormSliderProps> = React.memo(({ label, ...sliderProps }) => (
  <Box>
    <Typography gutterBottom id={`${sliderProps.name}-label`}>
      {label}
    </Typography>
    <Slider aria-labelledby={`${sliderProps.name}-label`} {...sliderProps} />
  </Box>
));