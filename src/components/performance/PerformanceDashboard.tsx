import React, { useCallback } from 'react';
import {
  Alert,
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { usePerformance } from '../../hooks/usePerformance';
import { MetricCard } from './MetricCard';
import { SettingsGroup } from './SettingsGroup';
import { FormSwitch } from '../common/FormSwitch';
import { FormSlider } from '../common/FormSlider';

// Предполагаем, что тип настроек можно импортировать из хука или общего файла типов
type PerformanceSettings = ReturnType<typeof usePerformance>['settings'];

// Определяем тип для ключей приоритета для максимальной типобезопасности
type ProcessPriority = PerformanceSettings['optimization']['processPriority'];

export const PerformanceDashboard: React.FC = React.memo(() => {
  const { t } = useTranslation();
  const { metrics, settings, alerts, updatePerformanceSettings } = usePerformance();

  const handleSettingChange = useCallback(
    <S extends keyof PerformanceSettings, K extends keyof PerformanceSettings[S]>(
      section: S,
      key: K,
      value: PerformanceSettings[S][K]
    ) => {
      updatePerformanceSettings({
        ...settings,
        [section]: {
          ...settings[section],
          [key]: value,
        },
      });
    },
    [settings, updatePerformanceSettings]
  );

  const memoryUsage = (metrics.memory.used / metrics.memory.total) * 100;

  // Типобезопасный и правильный способ определения списка приоритетов
  const priorities: ProcessPriority[] = ['low', 'normal', 'high'];

  return (
    <>
      <Box
        sx={{
          p: 3,
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: 2,
        }}
      >
        <MetricCard label={t('performanceDashboard.fps')} value={metrics.fps.toFixed(1)} />
        <MetricCard label={t('performanceDashboard.memoryUsage')} value={memoryUsage.toFixed(1)} unit="%" />
        <MetricCard label={t('performanceDashboard.cpuUsage')} value={metrics.cpu.usage.toFixed(1)} unit="%" />
        <MetricCard label={t('performanceDashboard.networkLatency')} value={metrics.network.latency.toFixed(0)} unit="ms" />

        <Box
          sx={{
            gridColumn: '1 / -1',
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: 2,
          }}
        >
          <SettingsGroup title={t('performanceDashboard.monitoringSettings')}>
            <FormSwitch
              label={t('performanceDashboard.enableMonitoring')}
              checked={settings.monitoring.enabled}
              onChange={(_, checked) => handleSettingChange('monitoring', 'enabled', checked)}
            />
            <FormSlider
              label={t('performanceDashboard.monitoringInterval')}
              value={settings.monitoring.interval}
              min={100}
              max={5000}
              step={100}
              onChangeCommitted={(_, value) => handleSettingChange('monitoring', 'interval', value)}
              valueLabelDisplay="auto"
            />
          </SettingsGroup>

          <SettingsGroup title={t('performanceDashboard.optimizationSettings')}>
            <FormSwitch
              label={t('performanceDashboard.hardwareAcceleration')}
              checked={settings.optimization.hardwareAcceleration}
              onChange={(_, checked) => handleSettingChange('optimization', 'hardwareAcceleration', checked)}
            />
            <FormControl fullWidth>
              <InputLabel>{t('performanceDashboard.processPriority')}</InputLabel>
              <Select
                value={settings.optimization.processPriority}
                label={t('performanceDashboard.processPriority')}
                onChange={(e: SelectChangeEvent) =>
                  handleSettingChange('optimization', 'processPriority', e.target.value)
                }
              >
                {priorities.map(p => (
                  <MenuItem key={p} value={p}>
                    {t(`performanceDashboard.priorities.${p}`)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </SettingsGroup>

          <SettingsGroup title={t('performanceDashboard.networkSettings')}>
            <FormSwitch
              label={t('performanceDashboard.compression')}
              checked={settings.network.compression}
              onChange={(_, checked) => handleSettingChange('network', 'compression', checked)}
            />
            <FormSwitch
              label={t('performanceDashboard.prefetching')}
              checked={settings.network.prefetching}
              onChange={(_, checked) => handleSettingChange('network', 'prefetching', checked)}
            />
          </SettingsGroup>
        </Box>
      </Box>

      <Box
        sx={{
          position: 'fixed',
          bottom: 2,
          right: theme => theme.spacing(2),
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          maxWidth: 400,
          zIndex: 'tooltip',
        }}
      >
        {alerts.map(alert => (
          <Alert
            key={alert.id}
            severity={alert.type === 'error' ? 'error' : 'warning'}
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        ))}
      </Box>
    </>
  );
});
