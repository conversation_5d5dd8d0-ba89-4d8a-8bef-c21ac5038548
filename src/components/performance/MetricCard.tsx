import React from 'react';
import { Card, Typography } from '@mui/material';

interface MetricCardProps {
  label: string;
  value: string;
  unit?: string;
}

export const MetricCard: React.FC<MetricCardProps> = React.memo(({ label, value, unit }) => (
  <Card sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
    <Typography color="text.secondary">{label}</Typography>
    <Typography variant="h3" color="primary.main" fontWeight="bold">
      {value}
      {unit && (
        <Typography component="span" variant="h4" color="text.secondary" sx={{ ml: 0.5 }}>
          {unit}
        </Typography>
      )}
    </Typography>
  </Card>
));