import React, { useState } from 'react';
import { Box, Tabs, Tab, Typography } from '@mui/material';
import RecommendationSettings from '../../RecommendationSettings';
import AboutPlugins from './AboutPlugins';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const SettingsPage: React.FC = () => {
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange} aria-label="settings tabs">
          <Tab label="Recommendations" id="settings-tab-0" />
          <Tab label="About Plugins" id="settings-tab-1" />
        </Tabs>
      </Box>
      <TabPanel value={value} index={0}>
        <RecommendationSettings />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <AboutPlugins />
      </TabPanel>
    </Box>
  );
};

export default SettingsPage;