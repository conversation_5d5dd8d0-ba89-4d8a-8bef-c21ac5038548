import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  Typography,
  Divider,
} from '@mui/material';
import { useDevEx } from '../../../providers/DevExProvider';

const AboutPlugins: React.FC = () => {
  const { manager } = useDevEx();
  const plugins = manager.getPlugins();

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        About Loaded Plugins
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        This section displays all the internal plugins currently loaded by the DevExManager.
      </Typography>
      <List sx={{ bgcolor: 'background.paper' }}>
        {plugins.map((plugin, index) => (
          <React.Fragment key={plugin.name}>
            <ListItem alignItems="flex-start">
              <ListItemText
                primary={`${plugin.name} (v${plugin.version})`}
                secondary={plugin.description}
              />
            </ListItem>
            {index < plugins.length - 1 && <Divider component="li" />}
          </React.Fragment>
        ))}
      </List>
    </Box>
  );
};

export default AboutPlugins;