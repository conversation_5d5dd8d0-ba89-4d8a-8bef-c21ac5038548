import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  FormControlLabel,
  Switch,
  TextField,
  Toolt<PERSON>,
  Typography,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import React, { useState, useEffect, useCallback } from 'react';
import { IRecommendationRule } from '../../developer-experience/recommendations/rule.interface';
import { UserRuleConfig } from '../../developer-experience/recommendations/RecommendationEngine';
import { toast } from 'react-hot-toast';
import { useDevEx } from '../../providers/DevExProvider';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

interface RuleState {
  rule: IRecommendationRule<any>;
  userConfig: UserRuleConfig;
}

const RecommendationSettings: React.FC = () => {
  const [settings, setSettings] = useState<RuleState[]>([]);
  const { manager, updateRecommendationRuleConfig, resetRecommendationRulesToDefault } = useDevEx();

  const loadSettings = useCallback(() => {
    const currentSettings = manager.getRecommendationRulesWithConfig();
    setSettings(currentSettings);
  }, [manager]);

  useEffect(() => {
    loadSettings();
  }, [loadSettings, manager]);

  const handleToggle = (ruleId: string, isEnabled: boolean) => {
    setSettings(prevSettings =>
      prevSettings.map(s =>
        s.rule.id === ruleId ? { ...s, userConfig: { ...s.userConfig, enabled: isEnabled } } : s
      )
    );
  };

  const handleConfigChange = (ruleId: string, key: string, value: string) => {
    // Convert to number if the original value was a number
    const originalValue = settings.find(s => s.rule.id === ruleId)?.rule.defaultConfig[key];
    const parsedValue = typeof originalValue === 'number' ? parseFloat(value) || 0 : value;

    setSettings(prevSettings =>
      prevSettings.map(s =>
        s.rule.id === ruleId
          ? {
              ...s,
              userConfig: {
                ...s.userConfig,
                config: { ...s.userConfig.config, [key]: parsedValue },
              },
            }
          : s
      )
    );
  };

  const handleSave = useCallback(() => {
    try {
      settings.forEach(({ rule, userConfig }) => {
        updateRecommendationRuleConfig(rule.id, userConfig);
      });
      toast.success('Settings saved successfully!');
    } catch (error) {
      toast.error('Failed to save settings.');
      console.error(error);
    }
  }, [settings, updateRecommendationRuleConfig]);

  const handleReset = useCallback(() => {
    try {
      resetRecommendationRulesToDefault();
      toast.success('Settings reset to default!');
    } catch (error) {
      toast.error('Failed to reset settings.');
      console.error(error);
    }
  }, [resetRecommendationRulesToDefault]);

  return (
    <Box sx={{ p: 2, maxWidth: 800, margin: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Recommendation Settings
      </Typography>
      {settings.map(({ rule, userConfig }) => {
        const isEnabled = userConfig.enabled !== false;
        const currentConfig = { ...rule.defaultConfig, ...(userConfig.config ?? {}) };

        return (
          <Accordion key={rule.id} defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isEnabled}
                    onChange={e => handleToggle(rule.id, e.target.checked)}
                    onClick={e => e.stopPropagation()} // Prevent accordion from toggling
                  />
                }
                label={
                  <Typography variant="h6" component="div">
                    {rule.name}
                    {rule.description && (
                      <Tooltip title={rule.description} placement="top-start">
                        <InfoOutlinedIcon
                          sx={{ fontSize: '1rem', ml: 1, verticalAlign: 'middle', color: 'text.secondary' }}
                        />
                      </Tooltip>
                    )}
                  </Typography>
                }
              />
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>{rule.description}</Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {Object.entries(rule.defaultConfig).map(([key, defaultValue]) => (
                  <TextField
                    key={key}
                    label={key}
                    type={typeof defaultValue === 'number' ? 'number' : 'text'}
                    value={currentConfig[key]}
                    onChange={e => handleConfigChange(rule.id, key, e.target.value)}
                    variant="outlined"
                    fullWidth
                    disabled={!isEnabled}
                    helperText={
                      rule.parameterDescriptions[key]
                        ? `${rule.parameterDescriptions[key]} (Default: ${defaultValue})`
                        : `Default: ${defaultValue}`
                    }
                  />
                ))}
              </Box>
            </AccordionDetails>
          </Accordion>
        );
      })}
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button variant="text" color="secondary" onClick={handleReset}>
          Reset to Defaults
        </Button>
        <Button variant="contained" color="primary" onClick={handleSave}>
          Save All Settings
        </Button>
      </Box>
    </Box>
  );
};

export default RecommendationSettings;