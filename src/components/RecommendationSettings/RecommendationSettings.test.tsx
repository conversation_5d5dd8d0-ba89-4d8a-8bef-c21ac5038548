import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import RecommendationSettings from './index';
import { DevExProvider } from '../../providers/DevExProvider';
import { settingsPersistence } from '../../developer-experience/SettingsPersistence';

// Mock the persistence layer
vi.mock('../../developer-experience/SettingsPersistence');

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Helper to render with the actual provider
const renderComponent = () => render(
  <DevExProvider>
    <RecommendationSettings />
  </DevExProvider>
);

describe('RecommendationSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render rules and allow changing a value', async () => {
    // Arrange: mock loaded settings
    (settingsPersistence.load as jest.Mock).mockReturnValue({
      recommendations: {
        'slow-build-time': { config: { thresholdMs: 12000 } },
      },
    });
    renderComponent();
    const user = userEvent.setup();

    // Assert: initial state is correct
    const thresholdInput = await screen.findByLabelText('thresholdMs');
    expect(thresholdInput).toHaveValue(12000);

    // Act: change value and save
    await user.clear(thresholdInput);
    await user.type(thresholdInput, '8000');
    const saveButton = screen.getByRole('button', { name: /save all settings/i });
    await user.click(saveButton);

    // Assert: persistence was called with the new value
    expect(settingsPersistence.save).toHaveBeenCalledWith({
      recommendations: expect.objectContaining({
        'slow-build-time': { config: { thresholdMs: 8000 } },
      }),
    });
  });

  it('should allow disabling a rule', async () => {
    renderComponent();
    const user = userEvent.setup();

    const enableSwitch = await screen.findByLabelText('Slow Build Time');
    expect(enableSwitch).toBeChecked();

    await user.click(enableSwitch);
    expect(enableSwitch).not.toBeChecked();

    const saveButton = screen.getByRole('button', { name: /save all settings/i });
    await user.click(saveButton);

    expect(settingsPersistence.save).toHaveBeenCalledWith({
      recommendations: expect.objectContaining({
        'slow-build-time': { enabled: false },
      }),
    });
  });

  it('should reset all rules to default', async () => {
    // Arrange: mock some custom settings
    (settingsPersistence.load as jest.Mock).mockReturnValue({
      recommendations: {
        'slow-build-time': { config: { thresholdMs: 15000 } },
      },
    });
    renderComponent();
    const user = userEvent.setup();

    // Assert: custom value is loaded
    const thresholdInput = screen.getByLabelText('thresholdMs');
    expect(thresholdInput).toHaveValue(15000);

    // Act: reset
    const resetButton = screen.getByRole('button', { name: /reset to defaults/i });
    await user.click(resetButton);

    // Assert: persistence was called to clear settings
    expect(settingsPersistence.save).toHaveBeenCalledWith({ recommendations: {} });

    // Assert: UI updated to show default value
    expect(thresholdInput).toHaveValue(10000); // The default from BuildTimeRule
  });
});