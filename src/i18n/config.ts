import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import translationEN from './locales/en/translation.json';
import translationRU from './locales/ru/translation.json';

const resources = {
  en: {
    translation: translationEN,
  },
  ru: {
    translation: translationRU,
  },
};

i18n
  .use(LanguageDetector) // Автоматически определяет язык пользователя
  .use(initReactI18next) // Интегрирует i18next с React
  .init({
    resources,
    fallbackLng: 'en', // Язык по умолчанию, если язык пользователя не найден
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false, // React уже защищает от XSS
    },
  });

export default i18n;