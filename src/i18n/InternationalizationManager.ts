/**
 * Продвинутая система интернационализации и локализации
 * с поддержкой множественных языков, RTL, культурных особенностей
 */

import React, { useSyncExternalStore, useCallback } from 'react';
export interface I18nConfig {
  defaultLocale: string;
  supportedLocales: string[];
  fallbackLocale: string;
  enableRTL: boolean;
  enablePluralRules: boolean;
  enableNumberFormatting: boolean;
  enableDateFormatting: boolean;
  enableCurrencyFormatting: boolean;
  enableRelativeTime: boolean;
  enableContextualTranslations: boolean;
  loadStrategy: 'eager' | 'lazy' | 'on-demand';
  cacheTranslations: boolean;
  detectBrowserLanguage: boolean;
}

export interface LocaleData {
  code: string;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
  region: string;
  currency: string;
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    grouping: number[];
  };
  culturalSettings: CulturalSettings;
}

export interface PluralRule {
  category: 'zero' | 'one' | 'two' | 'few' | 'many' | 'other';
  condition: string;
  examples: number[];
}

export interface CulturalSettings {
  weekStart: number; // 0 = Sunday, 1 = Monday
  workingDays: number[];
  holidays: Holiday[];
  colorMeanings: Record<string, string>;
  gestureInterpretations: Record<string, string>;
  formalityLevels: string[];
  addressFormats: AddressFormat[];
}

export interface Holiday {
  name: string;
  date: string;
  type: 'national' | 'religious' | 'cultural';
  description: string;
}

export interface AddressFormat {
  country: string;
  format: string;
  fields: string[];
  required: string[];
}

export interface TranslationKey {
  key: string;
  namespace?: string;
  context?: string;
  count?: number;
  interpolation?: Record<string, any>;
}

export interface Translation {
  key: string;
  value: string | Record<string, string>;
  namespace: string;
  context?: string;
  metadata: {
    lastModified: Date;
    translator: string;
    reviewed: boolean;
    notes?: string;
  };
}

export interface TranslationMemory {
  source: string;
  target: string;
  similarity: number;
  context: string;
  usage: number;
}

export class InternationalizationManager {
  private config: I18nConfig;
  private currentLocale: string;
  private locales = new Map<string, LocaleData>();
  private translations = new Map<string, Map<string, Translation>>();
  private translationMemory: TranslationMemory[] = [];
  private formatters = new Map<string, any>();
  private observers: ((locale: string) => void)[] = [];

  constructor(config: Partial<I18nConfig> = {}) {
    this.config = {
      defaultLocale: 'en-US',
      supportedLocales: ['en-US'],
      fallbackLocale: 'en-US',
      enableRTL: true,
      enablePluralRules: true,
      enableNumberFormatting: true,
      enableDateFormatting: true,
      enableCurrencyFormatting: true,
      enableRelativeTime: true,
      enableContextualTranslations: true,
      loadStrategy: 'lazy',
      cacheTranslations: true,
      detectBrowserLanguage: true,
      ...config,
    };

    this.currentLocale = this.config.defaultLocale;
    this.initialize();
  }

  /**
   * Инициализация системы i18n
   */
  private async initialize(): Promise<void> {
    console.log('🌍 Initializing Internationalization Manager...');

    // Загружаем данные локалей
    await this.loadLocaleData();

    // Определяем язык браузера
    if (this.config.detectBrowserLanguage) {
      const browserLocale = this.detectBrowserLanguage();
      if (browserLocale && this.isLocaleSupported(browserLocale)) {
        this.currentLocale = browserLocale;
      }
    }

    // Загружаем переводы для текущей локали
    await this.loadTranslations(this.currentLocale);

    // Настраиваем форматтеры
    this.setupFormatters();

    // Применяем локаль
    this.applyLocale(this.currentLocale);

    console.log(`✅ I18n initialized with locale: ${this.currentLocale}`);
  }

  /**
   * Загружает данные локалей
   */
  private async loadLocaleData(): Promise<void> {
    const localeDataPromises = this.config.supportedLocales.map(async localeCode => {
      try {
        const localeData = await this.fetchLocaleData(localeCode);
        this.locales.set(localeCode, localeData);
      } catch (error) {
        console.warn(`Failed to load locale data for ${localeCode}:`, error);
      }
    });

    await Promise.all(localeDataPromises);
  }

  /**
   * Получает данные локали
   */
  private async fetchLocaleData(localeCode: string): Promise<LocaleData> {
    // В реальном проекте данные загружаются с сервера или из файлов
    const [language, region] = localeCode.split('-');

    return {
      code: localeCode,
      name: this.getLocaleName(localeCode),
      nativeName: this.getNativeLocaleName(localeCode),
      direction: this.isRTLLocale(localeCode) ? 'rtl' : 'ltr',
      region: region || language.toUpperCase(),
      currency: this.getLocaleCurrency(localeCode),
      dateFormat: this.getLocaleDateFormat(localeCode),
      timeFormat: this.getLocaleTimeFormat(localeCode),
      numberFormat: this.getLocaleNumberFormat(localeCode),
      culturalSettings: await this.getLocaleCulturalSettings(localeCode),
    };
  }

  /**
   * Загружает переводы для локали
   */
  private async loadTranslations(localeCode: string): Promise<void> {
    if (this.translations.has(localeCode)) {
      return; // Уже загружены
    }

    try {
      const translations = await this.fetchTranslations(localeCode);
      this.translations.set(localeCode, translations);

      console.log(`📚 Loaded ${translations.size} translations for ${localeCode}`);
    } catch (error) {
      console.error(`Failed to load translations for ${localeCode}:`, error);

      // Загружаем fallback переводы
      if (localeCode !== this.config.fallbackLocale) {
        await this.loadTranslations(this.config.fallbackLocale);
      }
    }
  }

  /**
   * Получает переводы с сервера
   */
  private async fetchTranslations(localeCode: string): Promise<Map<string, Translation>> {
    const translations = new Map<string, Translation>();

    // В реальном проекте загружаем с API или из файлов
    const response = await fetch(`/api/i18n/translations/${localeCode}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch translations: ${response.statusText}`);
    }

    const data = await response.json();

    Object.entries(data).forEach(([key, value]: [string, any]) => {
      translations.set(key, {
        key,
        value: value.text || value,
        namespace: value.namespace || 'default',
        context: value.context,
        metadata: {
          lastModified: new Date(value.lastModified || Date.now()),
          translator: value.translator || 'system',
          reviewed: value.reviewed || false,
          notes: value.notes,
        },
      });
    });

    return translations;
  }

  /**
   * Переводит текст
   */
  translate(keyOrOptions: string | TranslationKey, options?: Partial<TranslationKey>): string {
    const translationKey: TranslationKey =
      typeof keyOrOptions === 'string' ? { key: keyOrOptions, ...options } : keyOrOptions;

    const translation = this.getTranslation(translationKey);

    if (!translation) {
      console.warn(`Translation not found: ${translationKey.key}`);
      return translationKey.key; // Возвращаем ключ как fallback
    }

    let result = this.resolveTranslationValue(translation, translationKey);

    // Применяем интерполяцию
    if (translationKey.interpolation) {
      result = this.interpolate(result, translationKey.interpolation);
    }

    // Применяем правила множественного числа
    if (translationKey.count !== undefined && this.config.enablePluralRules) {
      result = this.applyPluralRules(result, translationKey.count);
    }

    return result;
  }

  /**
   * Получает перевод
   */
  private getTranslation(translationKey: TranslationKey): Translation | null {
    const localeTranslations = this.translations.get(this.currentLocale);
    const fullKey = this.buildFullKey(translationKey);

    // Ищем в текущей локали
    let translation = localeTranslations?.get(fullKey);

    // Ищем без контекста
    if (!translation && translationKey.context) {
      const keyWithoutContext = this.buildFullKey({ ...translationKey, context: undefined });
      translation = localeTranslations?.get(keyWithoutContext);
    }

    // Ищем в fallback локали
    if (!translation && this.currentLocale !== this.config.fallbackLocale) {
      const fallbackTranslations = this.translations.get(this.config.fallbackLocale);
      translation = fallbackTranslations?.get(fullKey);
    }

    return translation || null;
  }

  /**
   * Строит полный ключ перевода
   */
  private buildFullKey(translationKey: TranslationKey): string {
    const parts = [translationKey.namespace || 'default', translationKey.key];

    if (translationKey.context) {
      parts.push(`@${translationKey.context}`);
    }

    return parts.join(':');
  }

  /**
   * Разрешает значение перевода
   */
  private resolveTranslationValue(
    translation: Translation,
    translationKey: TranslationKey
  ): string {
    if (typeof translation.value === 'string') {
      return translation.value;
    }

    // Если значение - объект, ищем подходящий вариант
    if (typeof translation.value === 'object') {
      // Для множественного числа
      if (translationKey.count !== undefined) {
        const pluralForm = this.getPluralForm(translationKey.count);
        if (translation.value[pluralForm]) {
          return translation.value[pluralForm];
        }
      }

      // Для контекста
      if (translationKey.context && translation.value[translationKey.context]) {
        return translation.value[translationKey.context];
      }

      // Возвращаем первое доступное значение
      const firstValue = Object.values(translation.value)[0];
      return typeof firstValue === 'string' ? firstValue : String(firstValue);
    }

    return String(translation.value);
  }

  /**
   * Применяет интерполяцию
   */
  private interpolate(text: string, variables: Record<string, any>): string {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      const value = variables[key];
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Получает форму множественного числа
   */
  private getPluralForm(count: number): Intl.LDMLPluralRule {
    if (!this.config.enablePluralRules) {
      return count === 1 ? 'one' : 'other';
    }
    // Используем нативный и безопасный Intl.PluralRules
    const pluralRules = new Intl.PluralRules(this.currentLocale);
    return pluralRules.select(count);
  }

  /**
   * Получает индекс формы множественного числа
   */
  private getPluralFormIndex(count: number): number {
    const pluralForm = this.getPluralForm(count);
    const forms = ['zero', 'one', 'two', 'few', 'many', 'other'];
    return forms.indexOf(pluralForm);
  }
  /**
   * Форматирует число
   */
  formatNumber(number: number, options: Intl.NumberFormatOptions = {}): string {
    if (!this.config.enableNumberFormatting) {
      return number.toString();
    }

    const formatter = new Intl.NumberFormat(this.currentLocale, options);
    return formatter.format(number);
  }

  /**
   * Форматирует валюту
   */
  formatCurrency(
    amount: number,
    currency?: string,
    options: Intl.NumberFormatOptions = {}
  ): string {
    if (!this.config.enableCurrencyFormatting) {
      return amount.toString();
    }

    const locale = this.locales.get(this.currentLocale);
    const currencyCode = currency || locale?.currency || 'USD';

    const formatter = new Intl.NumberFormat(this.currentLocale, {
      style: 'currency',
      currency: currencyCode,
      ...options,
    });

    return formatter.format(amount);
  }

  /**
   * Форматирует дату
   */
  formatDate(date: Date, options: Intl.DateTimeFormatOptions = {}): string {
    if (!this.config.enableDateFormatting) {
      return date.toLocaleDateString();
    }

    const formatter = new Intl.DateTimeFormat(this.currentLocale, options);
    return formatter.format(date);
  }

  /**
   * Форматирует относительное время
   */
  formatRelativeTime(value: number, unit: Intl.RelativeTimeFormatUnit): string {
    if (!this.config.enableRelativeTime) {
      return `${value} ${unit}`;
    }

    const formatter = new Intl.RelativeTimeFormat(this.currentLocale, {
      numeric: 'auto',
    });

    return formatter.format(value, unit);
  }

  /**
   * Изменяет текущую локаль
   */
  async changeLocale(localeCode: string): Promise<void> {
    if (!this.isLocaleSupported(localeCode)) {
      throw new Error(`Locale ${localeCode} is not supported`);
    }

    console.log(`🔄 Changing locale to: ${localeCode}`);

    // Загружаем переводы если нужно
    await this.loadTranslations(localeCode);

    const previousLocale = this.currentLocale;
    this.currentLocale = localeCode;

    // Применяем новую локаль
    this.applyLocale(localeCode);

    // Уведомляем наблюдателей
    this.notifyObservers(localeCode);

    console.log(`✅ Locale changed from ${previousLocale} to ${localeCode}`);
  }

  /**
   * Применяет локаль к DOM
   */
  private applyLocale(localeCode: string): void {
    const locale = this.locales.get(localeCode);
    if (!locale) return;

    // Устанавливаем атрибуты HTML
    document.documentElement.lang = localeCode;
    document.documentElement.dir = locale.direction;

    // Применяем CSS классы для RTL
    if (locale.direction === 'rtl') {
      document.body.classList.add('rtl');
      document.body.classList.remove('ltr');
    } else {
      document.body.classList.add('ltr');
      document.body.classList.remove('rtl');
    }

    // Устанавливаем CSS переменные
    document.documentElement.style.setProperty('--locale-direction', locale.direction);
    document.documentElement.style.setProperty('--locale-code', localeCode);
  }

  /**
   * Настраивает форматтеры
   */
  private setupFormatters(): void {
    // Создаем кэшированные форматтеры для производительности
    this.formatters.set('number', new Intl.NumberFormat(this.currentLocale));
    this.formatters.set(
      'currency',
      new Intl.NumberFormat(this.currentLocale, { style: 'currency', currency: 'USD' })
    );
    this.formatters.set('date', new Intl.DateTimeFormat(this.currentLocale));
    this.formatters.set(
      'time',
      new Intl.DateTimeFormat(this.currentLocale, { timeStyle: 'short' })
    );
    this.formatters.set('relativeTime', new Intl.RelativeTimeFormat(this.currentLocale));
  }

  /**
   * Определяет язык браузера
   */
  private detectBrowserLanguage(): string | null {
    const languages = navigator.languages || [navigator.language];

    for (const lang of languages) {
      // Точное совпадение
      if (this.config.supportedLocales.includes(lang)) {
        return lang;
      }

      // Совпадение по языку (без региона)
      const languageCode = lang.split('-')[0];
      const matchingLocale = this.config.supportedLocales.find(locale =>
        locale.startsWith(languageCode)
      );

      if (matchingLocale) {
        return matchingLocale;
      }
    }

    return null;
  }

  /**
   * Проверяет поддержку локали
   */
  private isLocaleSupported(localeCode: string): boolean {
    return this.config.supportedLocales.includes(localeCode);
  }

  /**
   * Добавляет наблюдателя изменений локали
   */
  addLocaleObserver(callback: (locale: string) => void): void {
    this.observers.push(callback);
  }

  /**
   * Удаляет наблюдателя
   */
  removeLocaleObserver(callback: (locale: string) => void): void {
    const index = this.observers.indexOf(callback);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  /**
   * Уведомляет наблюдателей
   */
  private notifyObservers(locale: string): void {
    this.observers.forEach(callback => {
      try {
        callback(locale);
      } catch (error) {
        console.error('Error in locale observer:', error);
      }
    });
  }

  /**
   * Получает текущую локаль
   */
  getCurrentLocale(): string {
    return this.currentLocale;
  }

  /**
   * Gets supported locales
   */
  getSupportedLocales(): LocaleData[] {
    return Array.from(this.locales.values());
  }

  /**
   * Checks if locale is RTL
   */
  isRTL(): boolean {
    const locale = this.locales.get(this.currentLocale);
    return locale?.direction === 'rtl';
  }

  // Helper methods for getting locale data

  private getLocaleName(localeCode: string): string {
    const names: Record<string, string> = {
      'en-US': 'English (United States)',
    };
    return names[localeCode] || localeCode;
  }

  private getNativeLocaleName(localeCode: string): string {
    const names: Record<string, string> = {
      'en-US': 'English',
    };
    return names[localeCode] || localeCode;
  }

  private isRTLLocale(localeCode: string): boolean {
    const rtlLocales = ['ar', 'he', 'fa', 'ur'];
    const language = localeCode.split('-')[0];
    return rtlLocales.includes(language);
  }

  private getLocaleCurrency(localeCode: string): string {
    const currencies: Record<string, string> = {
      'en-US': 'USD',
    };
    return currencies[localeCode] || 'USD';
  }

  private getLocaleDateFormat(localeCode: string): string {
    const formats: Record<string, string> = {
      'en-US': 'MM/DD/YYYY',
    };
    return formats[localeCode] || 'MM/DD/YYYY';
  }

  private getLocaleTimeFormat(localeCode: string): string {
    const formats: Record<string, string> = {
      'en-US': 'h:mm A',
    };
    return formats[localeCode] || 'h:mm A';
  }

  private getLocaleNumberFormat(localeCode: string): LocaleData['numberFormat'] {
    const formats: Record<string, LocaleData['numberFormat']> = {
      'en-US': { decimal: '.', thousands: ',', grouping: [3] },
    };
    return formats[localeCode] || formats['en-US'];
  }

  private async getLocaleCulturalSettings(localeCode: string): Promise<CulturalSettings> {
    // В реальном проекте загружается с сервера
    const language = localeCode.split('-')[0];

    const defaultSettings: CulturalSettings = {
      weekStart: 1, // Monday
      workingDays: [1, 2, 3, 4, 5], // Monday to Friday
      holidays: [],
      colorMeanings: {},
      gestureInterpretations: {},
      formalityLevels: ['informal', 'formal'],
      addressFormats: [],
    };

    switch (language) {
      case 'ar':
        return {
          ...defaultSettings,
          weekStart: 6, // Saturday
          workingDays: [0, 1, 2, 3, 4], // Sunday to Thursday
          formalityLevels: ['informal', 'formal', 'very-formal'],
        };
      case 'ja':
        return {
          ...defaultSettings,
          formalityLevels: ['casual', 'polite', 'formal', 'very-formal'],
        };
      default:
        return defaultSettings;
    }
  }
}

// Global instance
export const i18nManager = new InternationalizationManager();

// React Hook for use in components
export function useTranslation(namespace?: string): any {
  const locale = useSyncExternalStore(
    i18nManager.addLocaleObserver.bind(i18nManager),
    () => i18nManager.getCurrentLocale(),
    () => i18nManager.getCurrentLocale() // for server snapshot
  );

  const t = useCallback(
    (key: string, options?: Partial<TranslationKey>) => {
      return i18nManager.translate({ key, namespace, ...options });
    },
    [namespace, locale] // Пересоздаем функцию `t` при смене локали
  );

  return {
    t,
    // Возвращаем методы напрямую, чтобы они всегда были актуальны
    locale,
    changeLocale: i18nManager.changeLocale.bind(i18nManager),
    isRTL: i18nManager.isRTL(),
    formatNumber: i18nManager.formatNumber.bind(i18nManager),
    formatCurrency: i18nManager.formatCurrency.bind(i18nManager),
    formatDate: i18nManager.formatDate.bind(i18nManager),
    formatRelativeTime: i18nManager.formatRelativeTime.bind(i18nManager),
  };
}
