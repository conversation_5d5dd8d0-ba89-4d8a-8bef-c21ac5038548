import { Box, CircularProgress, CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { Suspense, lazy, memo, useEffect, ReactNode } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Toaster } from 'react-hot-toast';
import { Provider } from 'react-redux';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';

import styles from './App.module.css';
import ErrorBoundaryComponent from './components/ErrorBoundary';
import NotificationList from './components/NotificationList';
import { AnalyticsProvider } from './providers/AnalyticsProvider';
import { CacheProvider } from './providers/CacheProvider';
import { ExtensionProvider } from './providers/ExtensionProvider';
import { I18nProvider } from './providers/I18nProvider';
import { NetworkProvider } from './providers/NetworkProvider';
import { NotificationProvider } from './providers/NotificationProvider';
import { PerformanceProvider } from './providers/PerformanceProvider';
import { SecurityProvider } from './providers/SecurityProvider';
import { StateProvider } from './providers/StateProvider';
import { StorageProvider } from './providers/StorageProvider';
import useSessionStore from './sessionManager';
import { DevExProvider } from './providers/DevExProvider';
import { store } from './store';
import { ThemeHandler } from './themeHandler';

// Lazy load components
const LazyNotificationList = lazy(() => import('./components/NotificationList'));
const SettingsPage = lazy(() => import('./components/pages/SettingsPage'));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (replacing cacheTime)
    },
  },
});

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = memo(({ error, resetErrorBoundary }) => (
  <Box
    role="alert"
    p={3}
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      backgroundColor: 'background.paper',
    }}
  >
    <h2>Something went wrong:</h2>
    <pre
      style={{
        padding: '1rem',
        backgroundColor: 'rgba(0,0,0,0.1)',
        borderRadius: '4px',
        maxWidth: '80%',
        overflow: 'auto',
      }}
    >
      {error.message}
    </pre>
    <button
      onClick={resetErrorBoundary}
      style={{
        marginTop: '1rem',
        padding: '0.5rem 1rem',
        backgroundColor: 'primary.main',
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer',
      }}
    >
      Try again
    </button>
  </Box>
));

ErrorFallback.displayName = 'ErrorFallback';

const LoadingFallback = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
    }}
  >
    <CircularProgress />
  </Box>
);

/**
 * Composes multiple React providers into a single component.
 * This helps to avoid deep nesting ("Provider Hell").
 */
const composeProviders = (...providers: React.ElementType[]): React.FC<{ children: ReactNode }> => {
  return ({ children }) =>
    providers.reduceRight(
      (acc, ProviderComponent) => <ProviderComponent>{acc}</ProviderComponent>,
      children
    );
};

const AppProviders = composeProviders(NotificationProvider, I18nProvider, AnalyticsProvider, SecurityProvider, PerformanceProvider, CacheProvider, NetworkProvider, StorageProvider, StateProvider, ExtensionProvider);

const MainLayout: React.FC = () => (
  <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
    <nav style={{ padding: '1rem', background: '#1e1e1e', display: 'flex', gap: '1rem' }}>
      <Link to="/" style={{ color: 'white' }}>Home</Link>
      <Link to="/settings" style={{ color: 'white' }}>Settings</Link>
    </nav>
    <Box component="main" sx={{ flexGrow: 1, overflow: 'auto' }}>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="/" element={<LazyNotificationList />} />
        </Routes>
      </Suspense>
    </Box>
  </Box>
);

const App: React.FC = () => {
  const restoreSession = useSessionStore(state => state.restoreSession);

  useEffect(() => {
    restoreSession();
  }, [restoreSession]);

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => {
        // Reset the state of your app here
        window.location.reload();
      }}
    >
      <QueryClientProvider client={queryClient}>
        <DevExProvider>
          <Provider store={store}>
            <ThemeHandler>
              <CssBaseline enableColorScheme />
              <Router>
                <AppProviders>
                  <MainLayout />
                </AppProviders>
              </Router>
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#333',
                    color: '#fff',
                  },
                }}
              />
            </ThemeHandler>
          </Provider>
        </DevExProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default memo(App);
