import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API we want to expose
const electronApi = {
  storage: {
    getItem: (key: string): Promise<string | null> => ipcRenderer.invoke('storage:getItem', key),
    setItem: (key: string, value: string): Promise<void> => ipcRenderer.invoke('storage:setItem', key, value),
    removeItem: (key: string): Promise<void> => ipcRenderer.invoke('storage:removeItem', key),
    clear: (): Promise<void> => ipcRenderer.invoke('storage:clear'),
    getAllKeys: (): Promise<string[]> => ipcRenderer.invoke('storage:getAllKeys'),
  },
  // You can expose other main process features here
};

// Expose the API to the renderer process under `window.electron`
contextBridge.exposeInMainWorld('electron', electronApi);

// It's a good practice to declare the type for the exposed API
// in a .d.ts file to get TypeScript intellisense in the renderer.
// e.g., src/types/electron.d.ts