import { EventEmitter } from 'events';

import { BrowserView, BrowserWindow } from 'electron';

interface Tab {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  isActive: boolean;
  isPinned: boolean;
  isMuted: boolean;
  isLoading: boolean;
  lastAccessed: number;
  view: BrowserView;
}

interface TabGroup {
  id: string;
  name: string;
  tabs: Tab[];
  color?: string;
}

export class TabManager extends EventEmitter {
  private static instance: TabManager;
  private tabs: Map<string, Tab>;
  private groups: Map<string, TabGroup>;
  private mainWindow: BrowserWindow;
  private activeTabId: string | null;
  private maxTabs: number;
  private tabHistory: string[];

  private constructor(window: BrowserWindow) {
    super();
    this.mainWindow = window;
    this.tabs = new Map();
    this.groups = new Map();
    this.activeTabId = null;
    this.maxTabs = 100;
    this.tabHistory = [];
  }

  public static getInstance(window: BrowserWindow): TabManager {
    if (!TabManager.instance) {
      TabManager.instance = new TabManager(window);
    }
    return TabManager.instance;
  }

  public async createTab(url: string, options: Partial<Tab> = {}): Promise<Tab> {
    const { fork } = await import('child_process');
    
    if (this.tabs.size >= this.maxTabs) {
      const oldestTabId = this.tabHistory[0];
      if (oldestTabId) {
        await this.closeTab(oldestTabId);
      }
      if (this.tabs.size >= this.maxTabs) {
        throw new Error('Maximum number of tabs reached after LRU cleanup');
      }
    }

    const tabProcess = fork(__dirname + '/tab-process.js', [
      '--tab-id',
      `t_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`
    ], {
      stdio: ['pipe', 'pipe', 'pipe', 'ipc'],
      env: {
        ...process.env,
        TAB_ISOLATION: '1'
      }
    });

    tabProcess.on('message', (msg) => {
      this.handleProcessMessage(msg);
    });

    tabProcess.on('error', (err) => {
      this.handleProcessError(err);
    });

    tabProcess.on('exit', (code) => {
      this.handleProcessExit(tabProcess, code);
    });

    const view = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: true,
      },
    });

    const tab: Tab = {
      id: Math.random().toString(36).substr(2, 9),
      url,
      title: 'New Tab',
      isActive: false,
      isPinned: false,
      isMuted: false,
      isLoading: true,
      lastAccessed: Date.now(),
      view,
      ...options,
    };

    this.tabs.set(tab.id, tab);
    this.mainWindow.addBrowserView(view);
    this.setTabBounds(tab);

    await view.webContents.loadURL(url);
    this.setupTabListeners(tab);

    return tab;
  }

  private setTabBounds(tab: Tab): void {
    const bounds = this.mainWindow.getBounds();
    tab.view.setBounds({
      x: 0,
      y: 40, // Account for toolbar
      width: bounds.width,
      height: bounds.height - 40,
    });
  }

  private setupTabListeners(tab: Tab): void {
    const { view } = tab;

    view.webContents.on('page-title-updated', (event, title) => {
      tab.title = title;
      this.emit('tab-updated', tab);
    });

    view.webContents.on('did-start-loading', () => {
      tab.isLoading = true;
      this.emit('tab-updated', tab);
    });

    view.webContents.on('did-stop-loading', () => {
      tab.isLoading = false;
      this.emit('tab-updated', tab);
    });

    view.webContents.on('page-favicon-updated', (event, favicons) => {
      tab.favicon = favicons[0];
      this.emit('tab-updated', tab);
    });
  }

  public async activateTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    // Hide all tabs
    this.tabs.forEach(t => t.view.setBounds({ x: 0, y: 0, width: 0, height: 0 }));

    // Show selected tab
    this.setTabBounds(tab);
    tab.isActive = true;
    tab.lastAccessed = Date.now();
    this.activeTabId = tabId;

    // Update history
    this.tabHistory = this.tabHistory.filter(id => id !== tabId);
    this.tabHistory.push(tabId);

    this.emit('tab-activated', tab);
  }

  public async closeTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    this.mainWindow.removeBrowserView(tab.view);
    this.tabs.delete(tabId);

    // Update history
    this.tabHistory = this.tabHistory.filter(id => id !== tabId);

    // Activate next tab if needed
    if (this.activeTabId === tabId) {
      const nextTab = this.tabHistory[this.tabHistory.length - 1];
      if (nextTab) {
        await this.activateTab(nextTab);
      }
    }

    this.emit('tab-closed', tabId);
  }

  public async createGroup(name: string, color?: string): Promise<TabGroup> {
    const group: TabGroup = {
      id: Math.random().toString(36).substr(2, 9),
      name,
      tabs: [],
      color,
    };

    this.groups.set(group.id, group);
    this.emit('group-created', group);
    return group;
  }

  public async addTabToGroup(tabId: string, groupId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    const group = this.groups.get(groupId);
    if (!tab || !group) return;

    group.tabs.push(tab);
    this.emit('tab-group-updated', group);
  }

  public async pinTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    tab.isPinned = true;
    this.emit('tab-updated', tab);
  }

  public async muteTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    tab.isMuted = true;
    tab.view.webContents.setAudioMuted(true);
    this.emit('tab-updated', tab);
  }

  public async reloadTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    await tab.view.webContents.reload();
  }

  public async goBack(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    if (tab.view.webContents.canGoBack()) {
      tab.view.webContents.goBack();
    }
  }

  public async goForward(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    if (tab.view.webContents.canGoForward()) {
      tab.view.webContents.goForward();
    }
  }

  public getTab(tabId: string): Tab | undefined {
    return this.tabs.get(tabId);
  }

  public getAllTabs(): Tab[] {
    return Array.from(this.tabs.values());
  }

  public getActiveTab(): Tab | undefined {
    return this.activeTabId ? this.tabs.get(this.activeTabId) : undefined;
  }

  public getTabHistory(): string[] {
    return [...this.tabHistory];
  }

  public async cleanup(): Promise<void> {
    this.tabs.forEach(tab => {
      this.mainWindow.removeBrowserView(tab.view);
    });
    this.tabs.clear();
    this.groups.clear();
    this.tabHistory = [];
    this.activeTabId = null;
  }

  private async handleProcessExit(tabId: string, code: number): Promise<void> {
    const procInfo = this.tabProcesses.get(tabId);
    if (!procInfo) return;

    if (code !== 0 && procInfo.restartCount < 3) {
      const delay = Math.min(1000 * 2 ** procInfo.restartCount, 30000);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      const newProcess = fork(__dirname + '/tab-process.js', [
        '--tab-id',
        tabId
      ], {
        stdio: ['pipe', 'pipe', 'pipe', 'ipc'],
        env: {
          ...process.env,
          TAB_ISOLATION: '1',
          RESOURCE_LIMITS: JSON.stringify({
            maxMemoryMB: 512,
            maxCPU: 30
          })
        },
        execArgv: ['--max-old-space-size=512']
      });

      procInfo.process = newProcess;
      procInfo.restartCount++;
      procInfo.lastRestart = Date.now();
      this.emit('tab-restarted', tabId);
    } else {
      this.tabProcesses.delete(tabId);
      this.emit('tab-crashed', tabId);
    }
  }

  private handleProcessError(err: Error, tabId: string): void {
    console.error(`Process error in tab ${tabId}:`, err);
    this.emit('tab-error', {
      tabId,
      error: err.message,
      timestamp: Date.now()
    });
  }
}