import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';

import { app } from 'electron';

export interface BrowserSettings {
  general: {
    language: string;
    startupBehavior: 'newTab' | 'homePage' | 'lastSession' | 'custom';
    homePage: string;
    searchEngine: string;
    downloadLocation: string;
    defaultZoom: number;
    enableSpellCheck: boolean;
    spellCheckLanguages: string[];
    enableAutoFill: boolean;
    enablePasswordManager: boolean;
    enableSync: boolean;
    syncInterval: number;
  };
  appearance: {
    theme: string;
    fontSize: number;
    fontFamily: string;
    enableAnimations: boolean;
    enableSmoothScrolling: boolean;
    enableHardwareAcceleration: boolean;
    enableTransparency: boolean;
    enableCustomCSS: boolean;
    customCSS: string;
  };
  privacy: {
    enableDoNotTrack: boolean;
    enableTrackingProtection: boolean;
    enableFingerprintingProtection: boolean;
    enableWebRTCProtection: boolean;
    enableCanvasFingerprintingProtection: boolean;
    enableAudioFingerprintingProtection: boolean;
    enableBatteryStatusProtection: boolean;
    enableWebGLFingerprintingProtection: boolean;
    enableFontFingerprintingProtection: boolean;
    enableMediaDevicesProtection: boolean;
    enableNotificationsProtection: boolean;
    enableGeolocationProtection: boolean;
    enableClipboardProtection: boolean;
    enableHistoryProtection: boolean;
    enableBookmarksProtection: boolean;
    enableDownloadsProtection: boolean;
    enableCookiesProtection: boolean;
    enableLocalStorageProtection: boolean;
    enableSessionStorageProtection: boolean;
    enableIndexedDBProtection: boolean;
    enableWebSQLProtection: boolean;
    enableCacheProtection: boolean;
    enableServiceWorkersProtection: boolean;
    enablePushNotificationsProtection: boolean;
    enableBackgroundSyncProtection: boolean;
    enablePermissionsProtection: boolean;
    enableAutoplayProtection: boolean;
    enableWebMIDIProtection: boolean;
    enableWebUSBProtection: boolean;
    enableWebBluetoothProtection: boolean;
    enableWebSerialProtection: boolean;
    enableWebHIDProtection: boolean;
    enableWebNFCProtection: boolean;
    enableWebXRProtection: boolean;
    enableWebAssemblyProtection: boolean;
    enableWebGL2Protection: boolean;
    enableWebGPUProtection: boolean;
    enableWebAudioProtection: boolean;
    enableWebSpeechProtection: boolean;
    enableWebVRProtection: boolean;
    enableWebARProtection: boolean;
    enableWebRTCPProtection: boolean;
    enableWebTransportProtection: boolean;
    enableWebCodecsProtection: boolean;
    enableWebMLProtection: boolean;
    enableWebNNProtection: boolean;
    enableWebGPUProtection: boolean;
    enableWebGL2Protection: boolean;
    enableWebAudioProtection: boolean;
    enableWebSpeechProtection: boolean;
    enableWebVRProtection: boolean;
    enableWebARProtection: boolean;
    enableWebRTCPProtection: boolean;
    enableWebTransportProtection: boolean;
    enableWebCodecsProtection: boolean;
    enableWebMLProtection: boolean;
    enableWebNNProtection: boolean;
  };
  security: {
    enableCSP: boolean;
    enableSandbox: boolean;
    enableWebSecurity: boolean;
    enableMixedContentBlocking: boolean;
    enableCertificateValidation: boolean;
    enableSafeBrowsing: boolean;
    enablePhishingProtection: boolean;
    enableMalwareProtection: boolean;
    enableUnwantedSoftwareProtection: boolean;
    enableSocialEngineeringProtection: boolean;
    enablePotentiallyUnwantedProgramsProtection: boolean;
    enableUncommonDownloadProtection: boolean;
    enableDangerousDownloadProtection: boolean;
    enableDangerousExtensionProtection: boolean;
    enableDangerousWebsiteProtection: boolean;
    enableDangerousContentProtection: boolean;
    enableDangerousScriptProtection: boolean;
    enableDangerousPluginProtection: boolean;
    enableDangerousFrameProtection: boolean;
    enableDangerousObjectProtection: boolean;
    enableDangerousStyleProtection: boolean;
    enableDangerousFontProtection: boolean;
    enableDangerousMediaProtection: boolean;
    enableDangerousWorkerProtection: boolean;
    enableDangerousServiceWorkerProtection: boolean;
    enableDangerousWebAssemblyProtection: boolean;
    enableDangerousWebGLProtection: boolean;
    enableDangerousWebGPUProtection: boolean;
    enableDangerousWebAudioProtection: boolean;
    enableDangerousWebSpeechProtection: boolean;
    enableDangerousWebVRProtection: boolean;
    enableDangerousWebARProtection: boolean;
    enableDangerousWebRTCPProtection: boolean;
    enableDangerousWebTransportProtection: boolean;
    enableDangerousWebCodecsProtection: boolean;
    enableDangerousWebMLProtection: boolean;
    enableDangerousWebNNProtection: boolean;
  };
  performance: {
    enableHardwareAcceleration: boolean;
    maxMemoryUsage: number;
    maxConcurrentDownloads: number;
    maxConcurrentUploads: number;
    maxConcurrentConnections: number;
    maxConcurrentRequests: number;
    maxConcurrentWebSockets: number;
    maxConcurrentWebRTCConnections: number;
    maxConcurrentWebWorkers: number;
    maxConcurrentServiceWorkers: number;
    maxConcurrentWebAssemblyInstances: number;
    maxConcurrentWebGLContexts: number;
    maxConcurrentWebGPUDevices: number;
    maxConcurrentWebAudioContexts: number;
    maxConcurrentWebSpeechRecognizers: number;
    maxConcurrentWebVRDevices: number;
    maxConcurrentWebARDevices: number;
    maxConcurrentWebRTCPeerConnections: number;
    maxConcurrentWebTransportConnections: number;
    maxConcurrentWebCodecsDecoders: number;
    maxConcurrentWebMLModels: number;
    maxConcurrentWebNNModels: number;
    cacheSize: number;
    diskCacheSize: number;
    memoryCacheSize: number;
    enableDiskCache: boolean;
    enableMemoryCache: boolean;
    enableServiceWorkerCache: boolean;
    enableIndexedDBCache: boolean;
    enableWebSQLCache: boolean;
    enableApplicationCache: boolean;
    enableFileSystemCache: boolean;
    enableWebGLShaderCache: boolean;
    enableWebGPUShaderCache: boolean;
    enableWebAudioBufferCache: boolean;
    enableWebSpeechModelCache: boolean;
    enableWebVRModelCache: boolean;
    enableWebARModelCache: boolean;
    enableWebRTCCodecCache: boolean;
    enableWebTransportCodecCache: boolean;
    enableWebCodecsCodecCache: boolean;
    enableWebMLModelCache: boolean;
    enableWebNNModelCache: boolean;
  };
  updates: {
    autoCheck: boolean;
    autoDownload: boolean;
    autoInstall: boolean;
    checkInterval: number;
    channel: string;
    allowPrerelease: boolean;
  };
  accessibility: {
    enableHighContrast: boolean;
    enableLargeText: boolean;
    enableScreenReader: boolean;
    enableBrailleDisplay: boolean;
    enableSpeechRecognition: boolean;
    enableSpeechSynthesis: boolean;
    enableKeyboardNavigation: boolean;
    enableMouseNavigation: boolean;
    enableTouchNavigation: boolean;
    enableGestureNavigation: boolean;
    enableVoiceControl: boolean;
    enableEyeTracking: boolean;
    enableHeadTracking: boolean;
    enableMotionControl: boolean;
    enableBrainControl: boolean;
    enableAssistiveTechnologies: boolean;
    enableAccessibilityTools: boolean;
    enableAccessibilityFeatures: boolean;
    enableAccessibilityOptions: boolean;
    enableAccessibilitySettings: boolean;
  };
  developer: {
    enableDevTools: boolean;
    enableRemoteDebugging: boolean;
    enableNodeIntegration: boolean;
    enableContextIsolation: boolean;
    enableSandbox: boolean;
    enableWebSecurity: boolean;
    enableMixedContent: boolean;
    enableCertificateValidation: boolean;
    enableSafeBrowsing: boolean;
    enablePhishingProtection: boolean;
    enableMalwareProtection: boolean;
    enableUnwantedSoftwareProtection: boolean;
    enableSocialEngineeringProtection: boolean;
    enablePotentiallyUnwantedProgramsProtection: boolean;
    enableUncommonDownloadProtection: boolean;
    enableDangerousDownloadProtection: boolean;
    enableDangerousExtensionProtection: boolean;
    enableDangerousWebsiteProtection: boolean;
    enableDangerousContentProtection: boolean;
    enableDangerousScriptProtection: boolean;
    enableDangerousPluginProtection: boolean;
    enableDangerousFrameProtection: boolean;
    enableDangerousObjectProtection: boolean;
    enableDangerousStyleProtection: boolean;
    enableDangerousFontProtection: boolean;
    enableDangerousMediaProtection: boolean;
    enableDangerousWorkerProtection: boolean;
    enableDangerousServiceWorkerProtection: boolean;
    enableDangerousWebAssemblyProtection: boolean;
    enableDangerousWebGLProtection: boolean;
    enableDangerousWebGPUProtection: boolean;
    enableDangerousWebAudioProtection: boolean;
    enableDangerousWebSpeechProtection: boolean;
    enableDangerousWebVRProtection: boolean;
    enableDangerousWebARProtection: boolean;
    enableDangerousWebRTCPProtection: boolean;
    enableDangerousWebTransportProtection: boolean;
    enableDangerousWebCodecsProtection: boolean;
    enableDangerousWebMLProtection: boolean;
    enableDangerousWebNNProtection: boolean;
  };
}

export class SettingsManager extends EventEmitter {
  private static instance: SettingsManager;
  private settings: BrowserSettings;
  private settingsPath: string;
  private isInitialized: boolean;

  private constructor() {
    super();
    this.settings = this.getDefaultSettings();
    this.settingsPath = path.join(app.getPath('userData'), 'settings.json');
    this.isInitialized = false;
    this.initialize();
  }

  public static getInstance(): SettingsManager {
    if (!SettingsManager.instance) {
      SettingsManager.instance = new SettingsManager();
    }
    return SettingsManager.instance;
  }

  private getDefaultSettings(): BrowserSettings {
    return {
      general: {
        language: 'en',
        startupBehavior: 'newTab',
        homePage: 'https://www.google.com',
        searchEngine: 'google',
        downloadLocation: app.getPath('downloads'),
        defaultZoom: 100,
        enableSpellCheck: true,
        spellCheckLanguages: ['en'],
        enableAutoFill: true,
        enablePasswordManager: true,
        enableSync: true,
        syncInterval: 1000 * 60 * 60, // 1 hour
      },
      appearance: {
        theme: 'light',
        fontSize: 16,
        fontFamily: 'system-ui',
        enableAnimations: true,
        enableSmoothScrolling: true,
        enableHardwareAcceleration: true,
        enableTransparency: true,
        enableCustomCSS: false,
        customCSS: '',
      },
      privacy: {
        enableDoNotTrack: true,
        enableTrackingProtection: true,
        enableFingerprintingProtection: true,
        enableWebRTCProtection: true,
        enableCanvasFingerprintingProtection: true,
        enableAudioFingerprintingProtection: true,
        enableBatteryStatusProtection: true,
        enableWebGLFingerprintingProtection: true,
        enableFontFingerprintingProtection: true,
        enableMediaDevicesProtection: true,
        enableNotificationsProtection: true,
        enableGeolocationProtection: true,
        enableClipboardProtection: true,
        enableHistoryProtection: true,
        enableBookmarksProtection: true,
        enableDownloadsProtection: true,
        enableCookiesProtection: true,
        enableLocalStorageProtection: true,
        enableSessionStorageProtection: true,
        enableIndexedDBProtection: true,
        enableWebSQLProtection: true,
        enableCacheProtection: true,
        enableServiceWorkersProtection: true,
        enablePushNotificationsProtection: true,
        enableBackgroundSyncProtection: true,
        enablePermissionsProtection: true,
        enableAutoplayProtection: true,
        enableWebMIDIProtection: true,
        enableWebUSBProtection: true,
        enableWebBluetoothProtection: true,
        enableWebSerialProtection: true,
        enableWebHIDProtection: true,
        enableWebNFCProtection: true,
        enableWebXRProtection: true,
        enableWebAssemblyProtection: false, // Разрешаем использование WebAssembly
        enableWebGL2Protection: true,
        enableWebGPUProtection: false, // Разрешаем использование WebGPU
        enableWebAudioProtection: true,
        enableWebSpeechProtection: true,
        enableWebVRProtection: true,
        enableWebARProtection: true,
        enableWebRTCPProtection: true,
        enableWebTransportProtection: true,
        enableWebCodecsProtection: true,
        enableWebMLProtection: true,
        enableWebNNProtection: true,
      },
      security: {
        enableCSP: true,
        enableSandbox: true,
        enableWebSecurity: true,
        enableMixedContentBlocking: true,
        enableCertificateValidation: true,
        enableSafeBrowsing: true,
        enablePhishingProtection: true,
        enableMalwareProtection: true,
        enableUnwantedSoftwareProtection: true,
        enableSocialEngineeringProtection: true,
        enablePotentiallyUnwantedProgramsProtection: true,
        enableUncommonDownloadProtection: true,
        enableDangerousDownloadProtection: true,
        enableDangerousExtensionProtection: true,
        enableDangerousWebsiteProtection: true,
        enableDangerousContentProtection: true,
        enableDangerousScriptProtection: true,
        enableDangerousPluginProtection: true,
        enableDangerousFrameProtection: true,
        enableDangerousObjectProtection: true,
        enableDangerousStyleProtection: true,
        enableDangerousFontProtection: true,
        enableDangerousMediaProtection: true,
        enableDangerousWorkerProtection: true,
        enableDangerousServiceWorkerProtection: true,
        enableDangerousWebAssemblyProtection: true,
        enableDangerousWebGLProtection: true,
        enableDangerousWebGPUProtection: true,
        enableDangerousWebAudioProtection: true,
        enableDangerousWebSpeechProtection: true,
        enableDangerousWebVRProtection: true,
        enableDangerousWebARProtection: true,
        enableDangerousWebRTCPProtection: true,
        enableDangerousWebTransportProtection: true,
        enableDangerousWebCodecsProtection: true,
        enableDangerousWebMLProtection: true,
        enableDangerousWebNNProtection: true,
      },
      performance: {
        enableHardwareAcceleration: true,
        maxMemoryUsage: 2048,
        maxConcurrentDownloads: 6,
        maxConcurrentUploads: 6,
        maxConcurrentConnections: 6,
        maxConcurrentRequests: 6,
        maxConcurrentWebSockets: 6,
        maxConcurrentWebRTCConnections: 6,
        maxConcurrentWebWorkers: 6,
        maxConcurrentServiceWorkers: 6,
        maxConcurrentWebAssemblyInstances: 24, // Увеличиваем лимит параллельных инстансов
        maxConcurrentWebGLContexts: 6,
        maxConcurrentWebGPUDevices: 12, // Увеличиваем лимит GPU устройств
        maxConcurrentWebAudioContexts: 6,
        maxConcurrentWebSpeechRecognizers: 6,
        maxConcurrentWebVRDevices: 6,
        maxConcurrentWebARDevices: 6,
        maxConcurrentWebRTCPeerConnections: 6,
        maxConcurrentWebTransportConnections: 6,
        maxConcurrentWebCodecsDecoders: 6,
        maxConcurrentWebMLModels: 6,
        maxConcurrentWebNNModels: 6,
        cacheSize: 1024,
        diskCacheSize: 1024,
        memoryCacheSize: 1024,
        enableDiskCache: true,
        enableMemoryCache: true,
        enableServiceWorkerCache: true,
        enableIndexedDBCache: true,
        enableWebSQLCache: true,
        enableApplicationCache: true,
        enableFileSystemCache: true,
        enableWebGLShaderCache: true,
        enableWebGPUShaderCache: true,
        enableWebAudioBufferCache: true,
        enableWebSpeechModelCache: true,
        enableWebVRModelCache: true,
        enableWebARModelCache: true,
        enableWebRTCCodecCache: true,
        enableWebTransportCodecCache: true,
        enableWebCodecsCodecCache: true,
        enableWebMLModelCache: true,
        enableWebNNModelCache: true,
      },
      updates: {
        autoCheck: true,
        autoDownload: true,
        autoInstall: false,
        checkInterval: 1000 * 60 * 60 * 24, // 24 hours
        channel: 'latest',
        allowPrerelease: false,
      },
      accessibility: {
        enableHighContrast: false,
        enableLargeText: false,
        enableScreenReader: false,
        enableBrailleDisplay: false,
        enableSpeechRecognition: false,
        enableSpeechSynthesis: false,
        enableKeyboardNavigation: true,
        enableMouseNavigation: true,
        enableTouchNavigation: true,
        enableGestureNavigation: true,
        enableVoiceControl: false,
        enableEyeTracking: false,
        enableHeadTracking: false,
        enableMotionControl: false,
        enableBrainControl: false,
        enableAssistiveTechnologies: false,
        enableAccessibilityTools: false,
        enableAccessibilityFeatures: false,
        enableAccessibilityOptions: false,
        enableAccessibilitySettings: false,
      },
      developer: {
        enableDevTools: false,
        enableRemoteDebugging: false,
        enableNodeIntegration: false,
        enableContextIsolation: true,
        enableSandbox: true,
        enableWebSecurity: true,
        enableMixedContent: false,
        enableCertificateValidation: true,
        enableSafeBrowsing: true,
        enablePhishingProtection: true,
        enableMalwareProtection: true,
        enableUnwantedSoftwareProtection: true,
        enableSocialEngineeringProtection: true,
        enablePotentiallyUnwantedProgramsProtection: true,
        enableUncommonDownloadProtection: true,
        enableDangerousDownloadProtection: true,
        enableDangerousExtensionProtection: true,
        enableDangerousWebsiteProtection: true,
        enableDangerousContentProtection: true,
        enableDangerousScriptProtection: true,
        enableDangerousPluginProtection: true,
        enableDangerousFrameProtection: true,
        enableDangerousObjectProtection: true,
        enableDangerousStyleProtection: true,
        enableDangerousFontProtection: true,
        enableDangerousMediaProtection: true,
        enableDangerousWorkerProtection: true,
        enableDangerousServiceWorkerProtection: true,
        enableDangerousWebAssemblyProtection: true,
        enableDangerousWebGLProtection: true,
        enableDangerousWebGPUProtection: true,
        enableDangerousWebAudioProtection: true,
        enableDangerousWebSpeechProtection: true,
        enableDangerousWebVRProtection: true,
        enableDangerousWebARProtection: true,
        enableDangerousWebRTCPProtection: true,
        enableDangerousWebTransportProtection: true,
        enableDangerousWebCodecsProtection: true,
        enableDangerousWebMLProtection: true,
        enableDangerousWebNNProtection: true,
      },
    };
  }

  private async initialize(): Promise<void> {
    try {
      await this.loadSettings();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize settings:', error);
      this.emit('initialization-error', error);
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const data = await fs.readFile(this.settingsPath, 'utf-8');
      const loadedSettings = JSON.parse(data);
      this.settings = { ...this.getDefaultSettings(), ...loadedSettings };
    } catch (error) {
      console.error('Failed to load settings:', error);
      // Use default settings if loading fails
      this.settings = this.getDefaultSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    try {
      await fs.writeFile(this.settingsPath, JSON.stringify(this.settings, null, 2));
      this.emit('settings-saved');
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.emit('save-error', error);
    }
  }

  public getSettings(): BrowserSettings {
    return { ...this.settings };
  }

  public async updateSettings(newSettings: Partial<BrowserSettings>): Promise<void> {
    this.settings = { ...this.settings, ...newSettings };
    await this.saveSettings();
    this.emit('settings-updated', this.settings);
  }

  public async resetSettings(): Promise<void> {
    this.settings = this.getDefaultSettings();
    await this.saveSettings();
    this.emit('settings-reset');
  }

  public async exportSettings(): Promise<string> {
    return JSON.stringify(this.settings, null, 2);
  }

  public async importSettings(settingsData: string): Promise<void> {
    const importedSettings = JSON.parse(settingsData);
    await this.updateSettings(importedSettings);
  }

  public isInitialized(): boolean {
    return this.isInitialized;
  }
}