import { DevExManager, ErrorBoundary, DevExConfig } from './DevExManager';
import { AnalyticsPlugin } from './plugins/AnalyticsPlugin';
import { IReportablePlugin } from './plugin.interface';
import { settingsPersistence } from './SettingsPersistence';

// Mock plugins to prevent their initialization logic from running
jest.mock('./plugins/ToolingPlugin');
jest.mock('./plugins/ErrorHandlingPlugin');
jest.mock('./plugins/PerformancePlugin');
jest.mock('./plugins/HotReloadPlugin');
jest.mock('./plugins/CodeGenerationPlugin');
jest.mock('./plugins/AnalyticsPlugin');
jest.mock('./plugins/NotificationPlugin');

const createDefaultConfig = (): DevExConfig => ({
  enableHotReload: true,
  enableTypeChecking: true,
  enableLinting: true,
  enableFormatting: true,
  enableDebugging: true,
  enableProfiling: true,
  enableCodeGeneration: true,
  enableSnippets: true,
  enableIntelliSense: true,
  enableErrorBoundaries: true,
  recommendations: {},
});

describe('DevExManager Event Bus', () => {
  let manager: DevExManager;
  let listener1: jest.Mock;
  let listener2: jest.Mock;

  beforeEach(() => {
    const config = createDefaultConfig();
    manager = new DevExManager({
      ...config,
      enableHotReload: false,
      enableProfiling: false,
      enableCodeGeneration: false,
    });
    listener1 = jest.fn();
    listener2 = jest.fn();
  });

  it('should subscribe a listener to an event and emit to it', () => {
    const errorPayload: ErrorBoundary = { id: '1' } as ErrorBoundary;
    manager.on('error:new', listener1);
    manager.emit('error:new', errorPayload);

    expect(listener1).toHaveBeenCalledTimes(1);
    expect(listener1).toHaveBeenCalledWith(errorPayload);
  });

  it('should allow multiple listeners for the same event', () => {
    const errorPayload: ErrorBoundary = { id: '2' } as ErrorBoundary;
    manager.on('error:new', listener1);
    manager.on('error:new', listener2);
    manager.emit('error:new', errorPayload);

    expect(listener1).toHaveBeenCalledTimes(1);
    expect(listener1).toHaveBeenCalledWith(errorPayload);
    expect(listener2).toHaveBeenCalledTimes(1);
    expect(listener2).toHaveBeenCalledWith(errorPayload);
  });

  it('should not throw an error when emitting an event with no listeners', () => {
    // This will now cause a compile-time error, which is good.
    // For the test, we cast to any to bypass the check and test the runtime behavior.
    expect(() => manager.emit('unheard:event' as any)).not.toThrow();
  });

  it('should handle different events independently', () => {
    const errorPayload: ErrorBoundary = { id: '3' } as ErrorBoundary;
    const hmrPayload = { duration: 100, paths: [] };

    manager.on('error:new', listener1);
    manager.on('hmr:update', listener2);

    manager.emit('error:new', errorPayload);

    expect(listener1).toHaveBeenCalledWith(errorPayload);
    expect(listener2).not.toHaveBeenCalled();
  });

  it('should continue to call other listeners if one throws an error', () => {
    const errorListener = jest.fn(() => {
      throw new Error('Listener failed');
    });
    // Mock console.error to suppress expected error output in test results
    const consoleErrorSpy = jest
      .spyOn(console, 'error')
      .mockImplementation(() => {});

    const errorPayload: ErrorBoundary = { id: '4' } as ErrorBoundary;
    manager.on('error:new', errorListener);
    manager.on('error:new', listener1);

    manager.emit('error:new', errorPayload);

    expect(errorListener).toHaveBeenCalledTimes(1);
    expect(listener1).toHaveBeenCalledTimes(1);
    expect(listener1).toHaveBeenCalledWith(errorPayload);
    expect(consoleErrorSpy).toHaveBeenCalled();

    // Clean up the spy
    consoleErrorSpy.mockRestore();
  });
});

describe('DevExManager Report Generation', () => {
  it('should dynamically collect data from IReportablePlugin instances', () => {
    // Configure the mock for AnalyticsPlugin before instantiating DevExManager
    (AnalyticsPlugin as jest.Mock).mockImplementation(() => {
      const plugin: IReportablePlugin = {
        name: 'analytics',
        initialize: jest.fn(),
        getReportData: jest.fn().mockReturnValue({
          analytics: {
            testValue: 'reported',
          },
        }),
      };
      return plugin;
    });

    // Now, when DevExManager is created, it will use our mocked implementation
    const manager = new DevExManager({
      enableHotReload: false,
      enableProfiling: false,
    });

    const report = manager.generateDevExReport();

    expect(report.plugins).toBeDefined();
    expect(report.plugins.analytics).toBeDefined();
    expect(report.plugins.analytics.testValue).toBe('reported');
  });
});

describe('DevExManager Settings', () => {
  it('should correctly store and return the config it was initialized with', () => {
    const initialConfig = {
      ...createDefaultConfig(),
      enableHotReload: false,
      recommendations: { 'some-rule': { enabled: false } },
    };
    const manager = new DevExManager(initialConfig);
    const config = manager.getConfig();
    expect(config.enableHotReload).toBe(false);
    expect(config.recommendations?.['some-rule']?.enabled).toBe(false);
  });
});