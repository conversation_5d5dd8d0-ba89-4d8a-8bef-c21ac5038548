import { DevExManager } from '../DevExManager';
import { IDevExPlugin } from '../plugin.interface';

export class NotificationPlugin implements IDevExPlugin {
  readonly name = 'notification';
  readonly version = '1.0.0';
  readonly description = 'Displays user-facing notifications for system events.';
  private manager: DevExManager | null = null;

  initialize(manager: DevExManager): void {
    this.manager = manager;
    console.log('🔔 Initializing Notification Plugin...');
    this.subscribeToEvents();
  }

  private subscribeToEvents(): void {
    this.manager?.on('hmr:update', ({ duration, paths }) => {
      this.showHmrNotification(duration, paths);
    });
  }

  private showHmrNotification(duration: number, paths: string[]): void {
    const message = `✅ HMR update successful in ${duration.toFixed(2)}ms. Files updated: ${paths.join(', ')}`;
    
    // In a real UI, this would trigger a toast notification.
    // For now, we log to the console to demonstrate functionality.
    console.log(`[NotificationPlugin] ${message}`);
  }
}