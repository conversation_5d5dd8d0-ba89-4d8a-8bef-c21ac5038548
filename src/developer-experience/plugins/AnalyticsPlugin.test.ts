import { AnalyticsPlugin } from './AnalyticsPlugin';
import { DevExManager, ErrorBoundary, DevExEventMap } from '../DevExManager';

// --- Mocks ---
const eventListeners: { [K in keyof DevExEventMap]?: ((...args: DevExEventMap[K]) => void)[] } = {};

const mockManager = {
  on: jest.fn().mockImplementation(<K extends keyof DevExEventMap>(event: K, handler: (...args: DevExEventMap[K]) => void) => {
    if (!eventListeners[event]) {
      eventListeners[event] = [];
    }
    eventListeners[event]!.push(handler);
  }),
} as unknown as DevExManager;

describe('AnalyticsPlugin', () => {
  let analyticsPlugin: AnalyticsPlugin;
  let consoleLogSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.clearAllMocks();
    // Clear listeners for each test
    for (const key in eventListeners) {
      delete eventListeners[key as keyof DevExEventMap];
    }
    analyticsPlugin = new AnalyticsPlugin();
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy.mockRestore();
  });

  it('should subscribe to "error:new" event on initialization', () => {
    analyticsPlugin.initialize(mockManager);
    expect(mockManager.on).toHaveBeenCalledWith(
      'error:new',
      expect.any(Function)
    );
  });

  it('should track an error when the "error:new" event is emitted', () => {
    analyticsPlugin.initialize(mockManager);

    const testError = new Error('Analytics test error');
    const errorBoundary: ErrorBoundary = {
      id: 'error-123',
      component: 'TestComponent',
      error: testError,
      errorInfo: {},
      timestamp: new Date(),
      stack: 'stack-trace-string',
      props: {},
      state: {},
    };

    // Simulate the event emission by calling the stored handler
    eventListeners['error:new']![0](errorBoundary);

    expect(consoleLogSpy).toHaveBeenCalledWith(
      '📈 Analytics Event Sent:',
      expect.objectContaining({
        eventName: 'developer_error_occurred',
        error_message: 'Analytics test error',
        component: 'TestComponent',
      })
    );
  });
});