import { CodeSnippet, DevelopmentTool } from '../DevExManager';
import { IRecommendationProviderPlugin } from '../plugin.interface';
import { IRecommendationRule } from '../recommendations/rule.interface';
import { WarningCountRule } from '../recommendations/rules/WarningCountRule';

export class ToolingPlugin implements IRecommendationProviderPlugin {
  readonly name = 'tooling';
  readonly version = '1.0.0';
  readonly description = 'Manages developer tools, commands, and code snippets.';
  private tools = new Map<string, DevelopmentTool>();
  private snippets = new Map<string, CodeSnippet>();

  initialize(): void {
    console.log('🔧 Initializing Tooling Plugin...');
    this.setupDevelopmentTools();
    this.setupCodeSnippets();
  }

  public getTools(): DevelopmentTool[] {
    return Array.from(this.tools.values());
  }

  public getSnippets(): CodeSnippet[] {
    return Array.from(this.snippets.values());
  }

  public getRecommendationRules(): IRecommendationRule[] {
    // This plugin provides rules related to tooling and code quality.
    return [new WarningCountRule()];
  }

  private addTool(tool: DevelopmentTool): void {
    this.tools.set(tool.id, tool);
  }

  private addSnippet(snippet: CodeSnippet): void {
    this.snippets.set(snippet.id, snippet);
  }

  private setupDevelopmentTools(): void {
    // TypeScript компилятор
    this.addTool({
      id: 'typescript',
      name: 'TypeScript Compiler',
      description: 'Type checking and compilation',
      category: 'build',
      enabled: true,
      config: {
        strict: true,
        noImplicitAny: true,
        skipLibCheck: true,
      },
      commands: [
        {
          id: 'tsc-check',
          name: 'Type Check',
          description: 'Run TypeScript type checking',
          command: 'tsc',
          args: ['--noEmit'],
          shortcut: 'Ctrl+Shift+T',
        },
      ],
    });

    // ESLint
    this.addTool({
      id: 'eslint',
      name: 'ESLint',
      description: 'JavaScript and TypeScript linting',
      category: 'lint',
      enabled: true,
      config: {
        extends: ['@typescript-eslint/recommended', 'prettier'],
        rules: {
          '@typescript-eslint/no-unused-vars': 'error',
          '@typescript-eslint/no-explicit-any': 'warn',
        },
      },
      commands: [
        {
          id: 'eslint-check',
          name: 'Lint Code',
          description: 'Run ESLint on codebase',
          command: 'eslint',
          args: ['src/**/*.{ts,tsx}'],
          shortcut: 'Ctrl+Shift+L',
        },
        {
          id: 'eslint-fix',
          name: 'Fix Lint Issues',
          description: 'Auto-fix ESLint issues',
          command: 'eslint',
          args: ['src/**/*.{ts,tsx}', '--fix'],
          shortcut: 'Ctrl+Shift+F',
        },
      ],
    });
  }

  private setupCodeSnippets(): void {
    // React компонент
    this.addSnippet({
      id: 'react-component',
      name: 'React Functional Component',
      description: 'Create a new React functional component',
      language: 'typescript',
      prefix: 'rfc',
      body: [
        "import React from 'react';",
        '',
        'interface ${1:ComponentName}Props {',
        '  ${2:// props}',
        '}',
        '',
        'export const ${1:ComponentName}: React.FC<${1:ComponentName}Props> = ({',
        '  ${3:// destructured props}',
        '}) => {',
        '  return (',
        '    <div>',
        '      ${4:// component content}',
        '    </div>',
        '  );',
        '};',
        '',
        'export default ${1:ComponentName};',
      ],
      scope: 'typescript,typescriptreact',
    });

    // Custom Hook
    this.addSnippet({
      id: 'react-hook',
      name: 'React Custom Hook',
      description: 'Create a custom React hook',
      language: 'typescript',
      prefix: 'rhook',
      body: [
        "import { useState, useEffect } from 'react';",
        '',
        'export const use${1:HookName} = (${2:initialParams}) => {',
        '  const [${3:state}, set${4:State}] = useState(${5:initialValue});',
        '',
        '  useEffect(() => {',
        '    // effect logic',
        '  }, [/* dependencies */]);',
        '',
        '  return {',
        '    ${3:state},',
        '    set${4:State},',
        '    // other return values',
        '  };',
        '};',
      ],
      scope: 'typescript,typescriptreact',
    });
  }
}