import { DevExManager, ErrorBoundary } from '../DevExManager';
import { IReportablePlugin } from '../plugin.interface';

export class AnalyticsPlugin implements IReportablePlugin {
  readonly name = 'analytics';
  readonly version = '1.0.0';
  readonly description = 'Provides analytics tracking for developer events.';
  private manager: DevExManager | null = null;
  private trackedErrorCount = 0;

  initialize(manager: DevExManager): void {
    this.manager = manager;
    console.log('📊 Initializing Analytics Plugin...');
    this.subscribeToEvents();
  }

  private subscribeToEvents(): void {
    // The type for `errorBoundary` is now inferred correctly from the event map!
    this.manager?.on('error:new', errorBoundary => {
      this.trackError(errorBoundary);
    });
  }

  private trackError(error: ErrorBoundary): void {
    const analyticsPayload = {
      eventName: 'developer_error_occurred',
      timestamp: error.timestamp,
      error_message: error.error.message,
      component: error.component,
      stack_trace_hash: this.hashString(error.stack), // Simple hash to group similar errors
    };

    // In a real application, this would send data to a service like Mixpanel, Amplitude, or a custom endpoint.
    console.log('📈 Analytics Event Sent:', analyticsPayload);
    this.trackedErrorCount++;
  }

  /**
   * A simple hashing function for demonstration purposes.
   * @param str The string to hash.
   * @returns A numeric hash.
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash |= 0; // Convert to 32bit integer
    }
    return hash;
  }

  public getReportData(): { [key: string]: any } | null {
    return {
      analytics: {
        trackedErrors: this.trackedErrorCount,
      },
    };
  }
}