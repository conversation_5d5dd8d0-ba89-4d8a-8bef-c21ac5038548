import { DevExManager } from '../DevExManager';
import { IDevExPlugin } from '../plugin.interface';

export class HotReloadPlugin implements IDevExPlugin {
  readonly name = 'hot-reload';
  readonly version = '1.0.0';
  readonly description = 'Integrates with Vite HMR to provide hot-reloading metrics.';
  private manager: DevExManager | null = null;
  private hmrPayload: { startTime: number; paths: string[] } | null = null;

  initialize(manager: DevExManager): void {
    this.manager = manager;
    const config = this.manager.getConfig();

    if (!config.enableHotReload) return;

    console.log('🔥 Initializing Hot Reload Plugin...');
    this.setupHotReload();
  }

  private setupHotReload(): void {
    // Настройка Vite HMR (Hot Module Replacement)
    if (import.meta.hot) {
      import.meta.hot.on('vite:beforeUpdate', payload => {
        this.hmrPayload = {
          startTime: performance.now(),
          paths: payload.updates.map(u => u.path),
        };
        console.log(`[HMR] Updating modules:`, this.hmrPayload.paths);
      });
      import.meta.hot.on('vite:afterUpdate', () => {
        if (this.hmrPayload) {
          const duration = performance.now() - this.hmrPayload.startTime;
          this.manager?.updateMetric('hotReloadTime', duration);
          this.manager?.emit('hmr:update', {
            duration,
            paths: this.hmrPayload.paths,
          });
          console.log(`[HMR] Update took ${duration.toFixed(2)}ms`);
          this.hmrPayload = null;
        }
      });
    }
  }
}