import { PerformancePlugin } from './PerformancePlugin';
import { DevExManager } from '../DevExManager';

// --- <PERSON>cks ---
// Mock PerformanceObserver
const mockObserve = jest.fn();
const mockDisconnect = jest.fn();
let observerCallback: (list: { getEntries: () => any[] }) => void;

global.PerformanceObserver = jest.fn(callback => {
  observerCallback = callback;
  return {
    observe: mockObserve,
    disconnect: mockDisconnect,
    takeRecords: jest.fn(),
  };
}) as any;

// Mock performance.memory
(global.performance as any).memory = {
  usedJSHeapSize: 1000000,
  totalJSHeapSize: 2000000,
  jsHeapSizeLimit: 5000000,
};

// Mock DevExManager
const mockManager = {
  updateMetric: jest.fn(),
  getConfig: jest.fn().mockReturnValue({ enableProfiling: true }),
} as unknown as DevExManager;

describe('PerformancePlugin', () => {
  let performancePlugin: PerformancePlugin;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers(); // Use fake timers for setInterval
    performancePlugin = new PerformancePlugin();
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.useRealTimers(); // Restore real timers
  });

  it('should initialize and set up monitoring if enabled', () => {
    performancePlugin.initialize(mockManager);
    expect(global.PerformanceObserver).toHaveBeenCalled();
    expect(mockObserve).toHaveBeenCalledWith({
      entryTypes: ['measure', 'navigation', 'paint'],
    });
    expect(setInterval).toHaveBeenCalledWith(expect.any(Function), 5000);
  });

  it('should not initialize if profiling is disabled', () => {
    const disabledMockManager = {
      ...mockManager,
      getConfig: jest.fn().mockReturnValue({ enableProfiling: false }),
    } as unknown as DevExManager;

    performancePlugin.initialize(disabledMockManager);
    expect(global.PerformanceObserver).not.toHaveBeenCalled();
    expect(setInterval).not.toHaveBeenCalled();
  });

  it('should process a "navigation" performance entry', () => {
    performancePlugin.initialize(mockManager);
    const navEntry = {
      entryType: 'navigation',
      loadEventEnd: 1500,
      fetchStart: 500,
    };
    observerCallback({ getEntries: () => [navEntry] });
    expect(mockManager.updateMetric).toHaveBeenCalledWith('buildTime', 1000);
  });

  it('should process a "paint" performance entry for FCP', () => {
    performancePlugin.initialize(mockManager);
    const paintEntry = {
      entryType: 'paint',
      name: 'first-contentful-paint',
      startTime: 123.45,
    };
    observerCallback({ getEntries: () => [paintEntry] });
    expect(mockManager.updateMetric).toHaveBeenCalledWith('renderTime', 123.45);
  });

  it('should update memory metrics periodically', () => {
    performancePlugin.initialize(mockManager);
    expect(performancePlugin.getProfiles()).toHaveLength(0);

    // Fast-forward time to trigger setInterval
    jest.advanceTimersByTime(5000);

    const profiles = performancePlugin.getProfiles();
    expect(profiles).toHaveLength(1);
    expect(profiles[0].name).toBe('Memory Usage');
    expect(profiles[0].memoryUsage).toBe(1000000);
  });

  it('should calculate average memory usage correctly', () => {
    performancePlugin.initialize(mockManager);
    
    jest.advanceTimersByTime(5000); // First update (memoryUsage: 1000000)
    (global.performance as any).memory.usedJSHeapSize = 2000000;
    jest.advanceTimersByTime(5000); // Second update (memoryUsage: 2000000)

    expect(performancePlugin.getProfiles()).toHaveLength(2);
    const avg = performancePlugin.calculateAverageMemoryUsage();
    expect(avg).toBe(1500000);
  });

  it('should return 0 for average memory usage when no profiles exist', () => {
    const avg = performancePlugin.calculateAverageMemoryUsage();
    expect(avg).toBe(0);
  });
});