import { ErrorHandlingPlugin } from './ErrorHandlingPlugin';
import { DevExManager } from '../DevExManager';

// Mock the DevExManager to avoid full instantiation
const mockManager = {
  incrementMetric: jest.fn(),
  emit: jest.fn(),
} as unknown as DevExManager;

describe('ErrorHandlingPlugin', () => {
  let errorPlugin: ErrorHandlingPlugin;
  let addEventListenerSpy: jest.SpyInstance;
  const eventListeners: { [key: string]: (event: any) => void } = {};

  beforeEach(() => {
    // Reset mocks and plugin instance before each test
    jest.clearAllMocks();
    errorPlugin = new ErrorHandlingPlugin();

    // Spy on window.addEventListener and store the handlers
    addEventListenerSpy = jest
      .spyOn(window, 'addEventListener')
      .mockImplementation((event, handler) => {
        eventListeners[event] = handler as (event: any) => void;
      });

    // Mock console.error to prevent logging during tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore original implementations
    addEventListenerSpy.mockRestore();
  });

  it('should initialize and set up global error handlers', () => {
    errorPlugin.initialize(mockManager);
    expect(addEventListenerSpy).toHaveBeenCalledWith(
      'error',
      expect.any(Function)
    );
    expect(addEventListenerSpy).toHaveBeenCalledWith(
      'unhandledrejection',
      expect.any(Function)
    );
  });

  it('should handle a global error event', () => {
    errorPlugin.initialize(mockManager);
    const testError = new Error('Test global error');
    const errorEvent = {
      error: testError,
      filename: 'test.js',
      lineno: 10,
      colno: 5,
    };

    // Simulate the event
    eventListeners['error'](errorEvent);

    const errors = errorPlugin.getErrors();
    expect(errors).toHaveLength(1);
    expect(errors[0].error).toBe(testError);
    expect(errors[0].errorInfo.filename).toBe('test.js');
    expect(mockManager.incrementMetric).toHaveBeenCalledWith('errorCount');
    expect(mockManager.emit).toHaveBeenCalledWith('error:new', errors[0]);
  });

  it('should clear errors correctly', () => {
    errorPlugin.initialize(mockManager);
    const testError = new Error('Test error');
    eventListeners['error']({ error: testError });

    expect(errorPlugin.getErrors()).toHaveLength(1);
    errorPlugin.clearErrors();
    expect(errorPlugin.getErrors()).toHaveLength(0);
  });
});