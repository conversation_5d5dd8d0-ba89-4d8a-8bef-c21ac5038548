import { DevExManager, PerformanceProfile } from '../DevExManager';
import { IRecommendationProviderPlugin } from '../plugin.interface';
import { BuildTimeRule } from '../recommendations/rules/BuildTimeRule';
import { BundleSizeRule } from '../recommendations/rules/BundleSizeRule';

export class PerformancePlugin implements IRecommendationProviderPlugin {
  readonly name = 'performance';
  readonly version = '1.0.0';
  readonly description = 'Monitors and reports on application performance metrics.';
  private performanceProfiles: PerformanceProfile[] = [];
  private manager: DevExManager | null = null;

  initialize(manager: DevExManager): void {
    this.manager = manager;
    const config = this.manager.getConfig();

    if (!config.enableProfiling) return;

    console.log('📈 Initializing Performance Plugin...');
    this.setupPerformanceMonitoring();
  }

  public getProfiles(): PerformanceProfile[] {
    return [...this.performanceProfiles];
  }

  public calculateAverageMemoryUsage(): number {
    if (this.performanceProfiles.length === 0) return 0;

    const total = this.performanceProfiles.reduce(
      (sum, profile) => sum + profile.memoryUsage,
      0
    );
    return total / this.performanceProfiles.length;
  }

  public getRecommendationRules() {
    return [new BuildTimeRule(), new BundleSizeRule()];
  }

  private setupPerformanceMonitoring(): void {
    // Performance Observer для мониторинга метрик
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry);
        }
      });

      observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
    }

    // Мониторинг памяти
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.updateMemoryMetrics(memory);
      }, 5000);
    }
  }

  private processPerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming;
        this.manager?.updateMetric(
          'buildTime',
          navEntry.loadEventEnd - navEntry.fetchStart
        );
        break;

      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          this.manager?.updateMetric('renderTime', entry.startTime);
        }
        break;

      case 'measure':
        console.log(
          `📊 Performance measure: ${entry.name} took ${entry.duration.toFixed(2)}ms`
        );
        break;
    }
  }

  private updateMemoryMetrics(memory: any): void {
    const profile: PerformanceProfile = {
      id: `profile-${Date.now()}`,
      name: 'Memory Usage',
      timestamp: new Date(),
      duration: 0,
      memoryUsage: memory.usedJSHeapSize,
      cpuUsage: 0,
      renderTime: 0,
      bundleSize: 0,
      metrics: {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
      },
    };

    this.performanceProfiles.push(profile);

    if (this.performanceProfiles.length > 100) {
      this.performanceProfiles.splice(0, this.performanceProfiles.length - 50);
    }
  }
}