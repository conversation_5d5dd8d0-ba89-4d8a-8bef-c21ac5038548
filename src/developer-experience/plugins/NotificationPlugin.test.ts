import { NotificationPlugin } from './NotificationPlugin';
import { DevExManager, DevExEventMap } from '../DevExManager';

// --- Mocks ---
const eventListeners: { [K in keyof DevExEventMap]?: ((...args: DevExEventMap[K]) => void)[] } = {};

const mockManager = {
  on: jest.fn().mockImplementation(<K extends keyof DevExEventMap>(event: K, handler: (...args: DevExEventMap[K]) => void) => {
    if (!eventListeners[event]) {
      eventListeners[event] = [];
    }
    eventListeners[event]!.push(handler);
  }),
} as unknown as DevExManager;

describe('NotificationPlugin', () => {
  let notificationPlugin: NotificationPlugin;
  let consoleLogSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.clearAllMocks();
    // Clear listeners for each test
    for (const key in eventListeners) {
      delete eventListeners[key as keyof DevExEventMap];
    }
    notificationPlugin = new NotificationPlugin();
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy.mockRestore();
  });

  it('should subscribe to "hmr:update" event on initialization', () => {
    notificationPlugin.initialize(mockManager);
    expect(mockManager.on).toHaveBeenCalledWith(
      'hmr:update',
      expect.any(Function)
    );
  });

  it('should show a notification when "hmr:update" event is emitted', () => {
    notificationPlugin.initialize(mockManager);

    const hmrPayload = { duration: 55.123, paths: ['/src/App.tsx'] };

    // Simulate the event emission by calling the stored handler
    eventListeners['hmr:update']![0](hmrPayload);

    const expectedMessage = `[NotificationPlugin] ✅ HMR update successful in 55.12ms. Files updated: /src/App.tsx`;
    expect(consoleLogSpy).toHaveBeenCalledWith(expectedMessage);
  });
});