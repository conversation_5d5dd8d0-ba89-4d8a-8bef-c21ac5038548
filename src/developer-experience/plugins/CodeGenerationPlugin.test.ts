import { CodeGenerationPlugin } from './CodeGenerationPlugin';
import { DevExManager } from '../DevExManager';
import { ToolingPlugin } from './ToolingPlugin';

// --- Mocks ---
const mockSnippets = [
  {
    id: 'react-component',
    name: 'React Component',
    body: [
      "import React from 'react';",
      'const ${1:ComponentName} = () => {',
      '  return <div>${2:Content}</div>;',
      '};',
      'export default ${ComponentName};',
    ],
  },
];

const mockToolingPlugin = {
  name: 'tooling',
  getSnippets: jest.fn().mockReturnValue(mockSnippets),
} as unknown as ToolingPlugin;

const mockManager = {
  getPlugin: jest.fn().mockImplementation(name => {
    if (name === 'tooling') {
      return mockToolingPlugin;
    }
    return undefined;
  }),
  getConfig: jest.fn().mockReturnValue({ enableCodeGeneration: true }),
} as unknown as DevExManager;

describe('CodeGenerationPlugin', () => {
  let codeGenPlugin: CodeGenerationPlugin;

  beforeEach(() => {
    jest.clearAllMocks();
    codeGenPlugin = new CodeGenerationPlugin();
    codeGenPlugin.initialize(mockManager);
  });

  it('should generate code by replacing variables', () => {
    const variables = { ComponentName: 'MyButton' };
    const expectedCode = [
      "import React from 'react';",
      'const MyButton = () => {',
      '  return <div>Content</div>;',
      '};',
      'export default MyButton;',
    ].join('\n');

    const generatedCode = codeGenPlugin.generateCode('react-component', variables);
    expect(generatedCode).toBe(expectedCode);
  });

  it('should clean up unused placeholders', () => {
    const expectedCode = [
      "import React from 'react';",
      'const ComponentName = () => {',
      '  return <div>Content</div>;',
      '};',
      'export default ComponentName;',
    ].join('\n');

    const generatedCode = codeGenPlugin.generateCode('react-component', {});
    expect(generatedCode).toBe(expectedCode);
  });

  it('should throw an error if the template ID is not found', () => {
    expect(() => {
      codeGenPlugin.generateCode('non-existent-template', {});
    }).toThrow('Template non-existent-template not found');
  });
});