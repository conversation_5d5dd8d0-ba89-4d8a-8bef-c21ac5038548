import { ToolingPlugin } from './ToolingPlugin';
import { DevExManager } from '../DevExManager';

// Mock the DevExManager as it's not needed for these tests
const mockManager = {} as DevExManager;

describe('ToolingPlugin', () => {
  let toolingPlugin: ToolingPlugin;

  beforeEach(() => {
    toolingPlugin = new ToolingPlugin();
    // Mock console.log to prevent logging during tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  it('should have no tools or snippets before initialization', () => {
    expect(toolingPlugin.getTools()).toHaveLength(0);
    expect(toolingPlugin.getSnippets()).toHaveLength(0);
  });

  it('should initialize and set up default tools and snippets', () => {
    // Initialize the plugin
    toolingPlugin.initialize(mockManager);

    // Check the state after initialization
    expect(toolingPlugin.getTools().length).toBeGreaterThan(0);
    expect(toolingPlugin.getSnippets().length).toBeGreaterThan(0);
  });

  it('should return the correct list of development tools after initialization', () => {
    toolingPlugin.initialize(mockManager);
    const tools = toolingPlugin.getTools();

    expect(Array.isArray(tools)).toBe(true);
    const eslintTool = tools.find(tool => tool.id === 'eslint');
    expect(eslintTool).toBeDefined();
    expect(eslintTool?.name).toBe('ESLint');
    expect(eslintTool?.category).toBe('lint');
  });

  it('should return the correct list of code snippets after initialization', () => {
    toolingPlugin.initialize(mockManager);
    const snippets = toolingPlugin.getSnippets();

    expect(Array.isArray(snippets)).toBe(true);
    const reactComponentSnippet = snippets.find(
      snippet => snippet.id === 'react-component'
    );
    expect(reactComponentSnippet).toBeDefined();
    expect(reactComponentSnippet?.name).toBe('React Functional Component');
    expect(reactComponentSnippet?.prefix).toBe('rfc');
  });
});