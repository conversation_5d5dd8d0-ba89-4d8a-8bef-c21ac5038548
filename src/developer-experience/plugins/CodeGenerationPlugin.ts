import { DevExManager } from '../DevExManager';
import { IDevExPlugin } from '../plugin.interface';
import type { ToolingPlugin } from './ToolingPlugin';

export class CodeGenerationPlugin implements IDevExPlugin {
  readonly name = 'code-generation';
  readonly version = '1.0.0';
  readonly description = 'Generates code from predefined snippets and templates.';
  private manager: DevExManager | null = null;
  private toolingPlugin: ToolingPlugin | null = null;

  initialize(manager: DevExManager): void {
    this.manager = manager;
    const config = this.manager.getConfig();

    if (!config.enableCodeGeneration) return;

    console.log('🤖 Initializing Code Generation Plugin...');
  }

  private getToolingPlugin(): ToolingPlugin {
    if (!this.toolingPlugin) {
      const plugin = this.manager?.getPlugin('tooling') as ToolingPlugin | undefined;
      if (!plugin) {
        throw new Error('ToolingPlugin is not available for code generation.');
      }
      this.toolingPlugin = plugin;
    }
    return this.toolingPlugin;
  }

  public generateCode(templateId: string, variables: Record<string, string>): string {
    const toolingPlugin = this.getToolingPlugin();
    const snippet = toolingPlugin.getSnippets().find(s => s.id === templateId);
    if (!snippet) {
      throw new Error(`Template ${templateId} not found`);
    }

    let code = snippet.body.join('\n');

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\$\\{${key}\\}|\\$${key}`, 'g');
      code = code.replace(regex, value);
    });

    code = code.replace(/\$\{\d+:([^}]+)\}/g, '$1');
    code = code.replace(/\$\{\d+\}/g, '');

    return code;
  }
}