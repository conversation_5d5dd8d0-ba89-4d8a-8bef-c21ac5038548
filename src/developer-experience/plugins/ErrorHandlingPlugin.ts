import { DevExManager, ErrorBoundary } from '../DevExManager';
import { IRecommendationProviderPlugin } from '../plugin.interface';
import { ErrorCountRule } from '../recommendations/rules/ErrorCountRule';

export class ErrorHandlingPlugin implements IRecommendationProviderPlugin {
  readonly name = 'error-handling';
  readonly version = '1.0.0';
  readonly description = 'Captures and reports global errors and unhandled promise rejections.';
  private errorBoundaries: ErrorBoundary[] = [];
  private manager: DevExManager | null = null;

  initialize(manager: DevExManager): void {
    console.log('🚨 Initializing Error Handling Plugin...');
    this.manager = manager;
    this.setupGlobalErrorHandlers();
  }

  public getErrors(): ErrorBoundary[] {
    return [...this.errorBoundaries];
  }

  public clearErrors(): void {
    this.errorBoundaries = [];
  }

  public getRecommendationRules() {
    return [new ErrorCountRule()];
  }

  private setupGlobalErrorHandlers(): void {
    // Глобальный обработчик ошибок
    window.addEventListener('error', event => {
      this.handleError(event.error, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    // Обработчик необработанных промисов
    window.addEventListener('unhandledrejection', event => {
      this.handleError(event.reason, {
        type: 'unhandledrejection',
      });
    });
  }

  private handleError(error: Error, errorInfo: any): void {
    const errorBoundary: ErrorBoundary = {
      id: `error-${Date.now()}`,
      component: errorInfo.componentStack || 'Unknown',
      error,
      errorInfo,
      timestamp: new Date(),
      stack: error.stack || '',
      props: {},
      state: {},
    };

    this.errorBoundaries.push(errorBoundary);
    this.manager?.incrementMetric('errorCount');

    // Emit an event for other plugins to consume
    this.manager?.emit('error:new', errorBoundary);

    // Отправляем ошибку в систему мониторинга
    this.reportError(errorBoundary);

    console.error('🚨 Error caught by Error Handling Plugin:', error);
  }

  private reportError(errorBoundary: ErrorBoundary): void {
    // Отправка ошибки в систему мониторинга
    console.log('📤 Reporting error to monitoring system:', errorBoundary.id);
  }
}