import { DevExConfig } from './DevExManager';

const SETTINGS_KEY = 'a14-devex-settings';

/**
 * Provides a simple mechanism to persist DevExManager settings to localStorage.
 */
export const settingsPersistence = {
  save(config: Partial<DevExConfig>): void {
    try {
      const configToSave = JSON.stringify(config);
      localStorage.setItem(SETTINGS_KEY, configToSave);
    } catch (error) {
      console.error('Failed to save DevEx settings:', error);
    }
  },

  load(): Partial<DevExConfig> | null {
    try {
      const savedConfig = localStorage.getItem(SETTINGS_KEY);
      return savedConfig ? (JSON.parse(savedConfig) as Partial<DevExConfig>) : null;
    } catch (error) {
      console.error('Failed to load DevEx settings:', error);
      return null;
    }
  },
};