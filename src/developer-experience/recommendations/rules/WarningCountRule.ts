import { DevExReport } from '../../DevExManager';
import { IRecommendationRule, RuleConfig } from '../rule.interface';

interface WarningCountRuleConfig extends RuleConfig {
  maxWarnings: number;
}

export class WarningCountRule implements IRecommendationRule<WarningCountRuleConfig> {
  readonly id = 'high-warning-count';
  readonly name = 'High Warning Count';
  readonly description = 'Notifies when the number of build or lint warnings exceeds a specified limit.';
  readonly defaultConfig: WarningCountRuleConfig = {
    maxWarnings: 20,
  };
  readonly parameterDescriptions = {
    maxWarnings: 'The maximum number of warnings allowed before triggering a recommendation.',
  };

  evaluate(report: DevExReport, config: WarningCountRuleConfig): string | null {
    if (report.metrics.warningCount > config.maxWarnings) {
      return `A high number of warnings detected (>${config.maxWarnings}). While not errors, addressing them can improve code quality and prevent future bugs.`;
    }
    return null;
  }
}