import { DevExReport } from '../../DevExManager';
import { IRecommendationRule, RuleConfig } from '../rule.interface';

interface BuildTimeRuleConfig extends RuleConfig {
  thresholdMs: number;
}

export class BuildTimeRule implements IRecommendationRule<BuildTimeRuleConfig> {
  readonly id = 'slow-build-time';
  readonly name = 'Slow Build Time';
  readonly description = 'Notifies when the application build time exceeds a specified threshold.';
  readonly defaultConfig: BuildTimeRuleConfig = {
    thresholdMs: 10000, // 10 seconds
  };
  readonly parameterDescriptions = {
    thresholdMs: 'The maximum allowed build time in milliseconds.',
  };

  evaluate(report: DevExReport, config: BuildTimeRuleConfig): string | null {
    if (report.metrics.buildTime > config.thresholdMs) {
      return `Build time is high (>${config.thresholdMs / 1000}s). Consider optimizing build configuration.`;
    }
    return null;
  }
}