import { DevExReport } from '../../DevExManager';
import { IRecommendationRule, RuleConfig } from '../rule.interface';

interface ErrorCountRuleConfig extends RuleConfig {
  maxErrors: number;
}

export class ErrorCountRule implements IRecommendationRule<ErrorCountRuleConfig> {
  readonly id = 'high-error-count';
  readonly name = 'High Error Count';
  readonly description = 'Notifies when the number of runtime errors exceeds a specified limit.';
  readonly defaultConfig: ErrorCountRuleConfig = {
    maxErrors: 10,
  };
  readonly parameterDescriptions = {
    maxErrors: 'The maximum number of errors allowed before triggering a recommendation.',
  };

  evaluate(report: DevExReport, config: ErrorCountRuleConfig): string | null {
    if (report.metrics.errorCount > config.maxErrors) {
      return `High error count detected (>${config.maxErrors}). Review error handling and add more tests.`;
    }
    return null;
  }
}