import { DevExReport } from '../../DevExManager';
import { IRecommendationRule, RuleConfig } from '../rule.interface';

interface BundleSizeRuleConfig extends RuleConfig {
  thresholdBytes: number;
}

export class BundleSizeRule implements IRecommendationRule<BundleSizeRuleConfig> {
  readonly id = 'large-bundle-size';
  readonly name = 'Large Bundle Size';
  readonly description = 'Notifies when the main application bundle size exceeds a specified threshold.';
  readonly defaultConfig: BundleSizeRuleConfig = {
    thresholdBytes: 1024 * 1024, // 1MB
  };
  readonly parameterDescriptions = {
    thresholdBytes: 'The maximum allowed bundle size in bytes.',
  };

  evaluate(report: DevExReport, config: BundleSizeRuleConfig): string | null {
    if (report.metrics.bundleSize > config.thresholdBytes) {
      return `Bundle size is large (>${(config.thresholdBytes / (1024 * 1024)).toFixed(1)}MB). Consider code splitting and tree shaking.`;
    }
    return null;
  }
}