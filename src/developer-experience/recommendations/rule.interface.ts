import { DevExReport } from '../DevExManager';

/**
 * A base type for any rule's configuration object.
 */
export type RuleConfig = {
  [key: string]: any;
};

/**
 * Defines the contract for a recommendation rule that can be processed by the RecommendationEngine.
 * It uses generics to provide strong typing for each rule's specific configuration.
 * @template T - The shape of the configuration object for this rule.
 */
export interface IRecommendationRule<T extends RuleConfig = RuleConfig> {
  /**
   * A unique identifier for the rule.
   */
  readonly id: string;

  /**
   * A human-readable name for the rule.
   */
  readonly name: string;

  /**
   * A user-friendly description of what the rule does.
   */
  readonly description: string;

  /**
   * The default configuration for this rule.
   */
  readonly defaultConfig: T;

  /**
   * User-friendly descriptions for each parameter in the configuration.
   */
  readonly parameterDescriptions: { [K in keyof T]?: string };

  /**
   * Evaluates the report data and returns a recommendation message if the condition is met.
   * @param report The full DevEx report.
   * @param config The current configuration for the rule.
   * @returns A string with the recommendation, or null if the rule does not apply.
   */
  evaluate(report: DevExReport, config: T): string | null;
}