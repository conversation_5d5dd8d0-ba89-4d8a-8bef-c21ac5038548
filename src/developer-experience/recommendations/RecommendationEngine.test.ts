import { RecommendationEngine } from './RecommendationEngine';
import { BuildTimeRule } from './rules/BuildTimeRule';
import { BundleSizeRule } from './rules/BundleSizeRule';
import { ErrorCountRule } from './rules/ErrorCountRule';
import { WarningCountRule } from './rules/WarningCountRule';
import { DevExReport, DevExMetrics } from '../DevExManager';

// Helper to create a mock report with default values
const createMockReport = (metrics: Partial<DevExMetrics>): DevExReport => ({
  timestamp: new Date(),
  metrics: {
    buildTime: 0,
    hotReloadTime: 0,
    testExecutionTime: 0,
    lintingTime: 0,
    typeCheckingTime: 0,
    bundleSize: 0,
    errorCount: 0,
    warningCount: 0,
    codeQuality: 0,
    developerSatisfaction: 0,
    ...metrics,
  },
  tools: { total: 0, enabled: 0, categories: {} },
  errors: { total: 0, recent: [] },
  performance: { profiles: [], averageMemoryUsage: 0 },
  plugins: {},
  recommendations: [],
});

describe('Recommendation Rules', () => {
  describe('BuildTimeRule', () => {
    const rule = new BuildTimeRule();
    it('should return a recommendation if build time is high', () => {
      const report = createMockReport({ buildTime: 11000 }); // Exceeds 10s default
      expect(rule.evaluate(report, rule.defaultConfig)).toContain('Build time is high (>10s)');
    });

    it('should return null if build time is acceptable', () => {
      const report = createMockReport({ buildTime: 5000 });
      expect(rule.evaluate(report, rule.defaultConfig)).toBeNull();
    });
  });

  describe('BundleSizeRule', () => {
    const rule = new BundleSizeRule();
    it('should return a recommendation if bundle size is large', () => {
      const report = createMockReport({ bundleSize: 2000000 });
      expect(rule.evaluate(report, rule.defaultConfig)).toContain('Bundle size is large (>1.0MB)');
    });

    it('should return null if bundle size is acceptable', () => {
      const report = createMockReport({ bundleSize: 500000 });
      expect(rule.evaluate(report, rule.defaultConfig)).toBeNull();
    });
  });

  describe('ErrorCountRule', () => {
    const rule = new ErrorCountRule();
    it('should return a recommendation if error count is high', () => {
      const report = createMockReport({ errorCount: 20 });
      expect(rule.evaluate(report, rule.defaultConfig)).toContain('High error count detected (>10)');
    });

    it('should return null if error count is acceptable', () => {
      const report = createMockReport({ errorCount: 5 });
      expect(rule.evaluate(report, rule.defaultConfig)).toBeNull();
    });
  });

  describe('WarningCountRule', () => {
    const rule = new WarningCountRule();
    it('should return a recommendation if warning count is high', () => {
      const report = createMockReport({ warningCount: 25 });
      expect(rule.evaluate(report, rule.defaultConfig)).toContain('A high number of warnings detected (>20)');
    });

    it('should return null if warning count is acceptable', () => {
      const report = createMockReport({ warningCount: 10 });
      expect(rule.evaluate(report, rule.defaultConfig)).toBeNull();
    });
  });
});

describe('RecommendationEngine', () => {
  const allRules = [new BuildTimeRule(), new ErrorCountRule(), new BundleSizeRule()];

  it('should generate multiple recommendations if multiple rules are met', () => {
    const engine = new RecommendationEngine(allRules);
    const report = createMockReport({ buildTime: 12000, errorCount: 15 });
    const recommendations = engine.generate(report);
    expect(recommendations).toHaveLength(2);
    expect(recommendations).toContainEqual(expect.stringContaining('Build time'));
    expect(recommendations).toContainEqual(expect.stringContaining('High error count'));
  });

  it('should generate no recommendations if no rules are met', () => {
    const engine = new RecommendationEngine(allRules);
    const report = createMockReport({ buildTime: 1000, errorCount: 1 });
    const recommendations = engine.generate(report);
    expect(recommendations).toHaveLength(0);
  });

  it('should work with an empty set of rules', () => {
    const engine = new RecommendationEngine([]);
    const report = createMockReport({ buildTime: 12000 });
    const recommendations = engine.generate(report);
    expect(recommendations).toHaveLength(0);
  });

  it('should use user-defined configuration to override default thresholds', () => {
    const userConfig = {
      'slow-build-time': { config: { thresholdMs: 5000 } }, // Lower the threshold
      'high-error-count': { config: { maxErrors: 3 } }, // Lower the threshold
    };
    const engine = new RecommendationEngine(allRules, userConfig);

    // This report would NOT trigger default rules, but SHOULD trigger overridden rules.
    const report = createMockReport({ buildTime: 6000, errorCount: 5 });
    const recommendations = engine.generate(report);

    expect(recommendations).toHaveLength(2);
    expect(recommendations).toContainEqual(expect.stringContaining('Build time is high (>5s)'));
    expect(recommendations).toContainEqual(expect.stringContaining('High error count detected (>3)'));
  });

  it('should use default config if user config for a rule is not provided', () => {
    const userConfig = { 'high-error-count': { config: { maxErrors: 5 } } }; // Only override one rule
    const engine = new RecommendationEngine(allRules, userConfig);
    const report = createMockReport({ buildTime: 11000 }); // Should trigger default build time rule
    const recommendations = engine.generate(report);
    expect(recommendations).toHaveLength(1);
    expect(recommendations[0]).toContain('Build time is high (>10s)');
  });

  it('should not generate a recommendation for a disabled rule', () => {
    const userConfig = {
      'slow-build-time': { enabled: false }, // Disable this rule
    };
    const engine = new RecommendationEngine(allRules, userConfig);

    // This report would normally trigger the build time rule
    const report = createMockReport({ buildTime: 12000 });
    const recommendations = engine.generate(report);

    expect(recommendations).toHaveLength(0);
  });

  it('should update its configuration at runtime', () => {
    const engine = new RecommendationEngine(allRules, {});
    const report = createMockReport({ buildTime: 8000 });

    // Initially, no recommendation
    expect(engine.generate(report)).toHaveLength(0);

    // Update config to be more strict
    engine.updateConfig({ 'slow-build-time': { config: { thresholdMs: 7000 } } });

    // Now, it should generate a recommendation
    const recommendations = engine.generate(report);
    expect(recommendations).toHaveLength(1);
    expect(recommendations[0]).toContain('Build time is high (>7s)');
  });
});