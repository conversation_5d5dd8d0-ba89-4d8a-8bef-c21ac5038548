import { DevExReport } from '../DevExManager';
import { IRecommendationRule, RuleConfig } from './rule.interface';

export type UserRuleConfig = {
  enabled?: boolean;
  config?: RuleConfig;
};

export type AllUserRulesConfig = Record<string, UserRuleConfig>;

/**
 * Processes a DevExReport against a set of rules to generate actionable recommendations.
 */
export class RecommendationEngine {
  private rules: IRecommendationRule[];
  private userConfig: AllUserRulesConfig;

  constructor(rules: IRecommendationRule<any>[] = [], userConfig: AllUserRulesConfig = {}) {
    this.rules = rules;
    this.userConfig = userConfig;
    console.log(
      `🧠 Recommendation Engine initialized with ${this.rules.length} rules.`
    );
  }
  
  /**
   * Updates the user configuration for the rules at runtime.
   * @param userConfig The new user configuration.
   */
  public updateConfig(userConfig: AllUserRulesConfig) {
    this.userConfig = userConfig;
  }

  public generate(report: DevExReport): string[] {
    const recommendations: string[] = [];
    for (const rule of this.rules) {
      const userRuleConfig = this.userConfig[rule.id] ?? {};
      
      // A rule is enabled by default unless explicitly disabled.
      const isEnabled = userRuleConfig.enabled !== false;

      if (isEnabled) {
        const finalConfig = { ...rule.defaultConfig, ...userRuleConfig.config };
        const result = rule.evaluate(report, finalConfig);
        if (result) {
          recommendations.push(result);
        }
      }
    }
    return recommendations;
  }
}