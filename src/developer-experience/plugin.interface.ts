import type { DevExManager } from './DevExManager';
import { IRecommendationRule } from './recommendations/rule.interface';

/**
 * Base interface for all DevExManager plugins.
 */
export interface IDevExPlugin {
  /**
   * The unique name of the plugin.
   */
  readonly name: string;

  /**
   * The version of the plugin, following semantic versioning.
   */
  readonly version: string;

  /**
   * A brief description of what the plugin does.
   */
  readonly description: string;

  /**
   * Initializes the plugin, setting up listeners and configurations.
   * @param manager - The instance of the DevExManager.
   */
  initialize(manager: DevExManager): void;
}

/**
 * An optional interface for plugins that can provide recommendation rules to the engine.
 */
export interface IRecommendationProviderPlugin extends IDevExPlugin {
  /**
   * Returns an array of recommendation rules to be registered with the engine.
   */
  getRecommendationRules(): IRecommendationRule<any>[];
}

/**
 * An optional interface for plugins that can contribute data to the DevExReport.
 */
export interface IReportablePlugin extends IDevExPlugin {
  /**
   * Returns a data object to be included in the report.
   * The key should be the plugin's name.
   */
  getReportData(): { [key: string]: any } | null;
}

/**
 * User-defined type guard to check if a plugin is a RecommendationProvider.
 */
export function isRecommendationProvider(plugin: IDevExPlugin): plugin is IRecommendationProviderPlugin {
  return 'getRecommendationRules' in plugin && typeof (plugin as any).getRecommendationRules === 'function';
}

/**
 * User-defined type guard to check if a plugin is Reportable.
 */
export function isReportable(plugin: IDevExPlugin): plugin is IReportablePlugin {
  return 'getReportData' in plugin && typeof (plugin as any).getReportData === 'function';
}