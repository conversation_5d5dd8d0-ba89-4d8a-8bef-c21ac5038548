import { IDevExPlugin, IRecommendationProviderPlugin, isRecommendationProvider, isReportable } from './plugin.interface';
import { ErrorHandlingPlugin } from './plugins/ErrorHandlingPlugin';
import { ToolingPlugin } from './plugins/ToolingPlugin';
import { PerformancePlugin } from './plugins/PerformancePlugin';
import { HotReloadPlugin } from './plugins/HotReloadPlugin';
import { CodeGenerationPlugin } from './plugins/CodeGenerationPlugin';
import { AnalyticsPlugin } from './plugins/AnalyticsPlugin';
import { NotificationPlugin } from './plugins/NotificationPlugin';
import { IRecommendationRule } from './recommendations/rule.interface';
import { AllUserRulesConfig, RecommendationEngine } from './recommendations/RecommendationEngine';

/** ... (остальные интерфейсы остаются без изменений) ... */

export interface DevExConfig {
  enableHotReload: boolean;
  enableTypeChecking: boolean;
  enableLinting: boolean;
  enableFormatting: boolean;
  enableDebugging: boolean;
  enableProfiling: boolean;
  enableCodeGeneration: boolean;
  enableSnippets: boolean;
  enableIntelliSense: boolean;
  enableErrorBoundaries: boolean;
  recommendations?: AllUserRulesConfig;
}

export interface DevelopmentTool {
  id: string;
  name: string;
  description: string;
  category: 'build' | 'debug' | 'test' | 'lint' | 'format' | 'analyze' | 'generate';
  enabled: boolean;
  config: Record<string, any>;
  commands: ToolCommand[];
}

export interface ToolCommand {
  id: string;
  name: string;
  description: string;
  command: string;
  args: string[];
  shortcut?: string;
  icon?: string;
}

export interface CodeSnippet {
  id: string;
  name: string;
  description: string;
  language: string;
  prefix: string;
  body: string[];
  scope?: string;
  variables?: Record<string, string>;
}

export interface ErrorBoundary {
  id: string;
  component: string;
  error: Error;
  errorInfo: any;
  timestamp: Date;
  stack: string;
  props: Record<string, any>;
  state: Record<string, any>;
}

export interface PerformanceProfile {
  id: string;
  name: string;
  timestamp: Date;
  duration: number;
  memoryUsage: number;
  cpuUsage: number;
  renderTime: number;
  bundleSize: number;
  metrics: Record<string, number>;
}

export interface DevExMetrics {
  buildTime: number;
  hotReloadTime: number;
  testExecutionTime: number;
  lintingTime: number;
  typeCheckingTime: number;
  bundleSize: number;
  errorCount: number;
  warningCount: number;
  codeQuality: number;
  developerSatisfaction: number;
}

export interface DevExEventMap {
  'error:new': [ErrorBoundary];
  'performance:profile': [PerformanceProfile];
  'hmr:update': [{ duration: number; paths: string[] }];
}

export class DevExManager {
  private config: DevExConfig;
  private plugins = new Map<string, IDevExPlugin>();
  private eventEmitter = new Map<string, ((...args: any[]) => void)[]>();
  private recommendationEngine: RecommendationEngine;
  private metrics: DevExMetrics;

  constructor(config: Partial<DevExConfig> = {}) {
    this.config = {
      enableHotReload: true,
      enableTypeChecking: true,
      enableLinting: true,
      enableFormatting: true,
      enableDebugging: true,
      enableProfiling: true,
      enableCodeGeneration: true,
      enableSnippets: true,
      enableIntelliSense: true,
      enableErrorBoundaries: true,
      recommendations: {
        // User can override rule configs here
      },
      ...config,
    };
    this.metrics = {
      buildTime: 0,
      hotReloadTime: 0,
      testExecutionTime: 0,
      lintingTime: 0,
      typeCheckingTime: 0,
      bundleSize: 0,
      errorCount: 0,
      warningCount: 0,
      codeQuality: 0,
      developerSatisfaction: 0,
    };

    this.initialize();
  }

  /**
   * Инициализация DevEx системы
   */
  private initialize(): void {
    console.log('🛠️ Initializing Developer Experience Manager...');

    // 1. Register all plugins
    this.registerPlugin(new ToolingPlugin());
    this.registerPlugin(new ErrorHandlingPlugin());
    this.registerPlugin(new PerformancePlugin());
    this.registerPlugin(new HotReloadPlugin());
    this.registerPlugin(new CodeGenerationPlugin());
    this.registerPlugin(new AnalyticsPlugin());
    this.registerPlugin(new NotificationPlugin());

    // 2. Collect recommendation rules from plugins that provide them
    const recommendationRules: IRecommendationRule[] = [];
    this.plugins.forEach(plugin => {
      // Use a type guard for cleaner, safer code
      if (isRecommendationProvider(plugin)) {
        recommendationRules.push(...plugin.getRecommendationRules());
      }
    });

    // 3. Initialize the engine with collected rules
    this.recommendationEngine = new RecommendationEngine(recommendationRules, this.config.recommendations ?? {});

    // 4. Initialize all plugins (pass manager instance, set up listeners, etc.)
    this.plugins.forEach(plugin => plugin.initialize(this));

    console.log('✅ Developer Experience Manager initialized');
  }

  registerPlugin(plugin: IDevExPlugin) {
    if (this.plugins.has(plugin.name)) {
      console.warn(`Plugin with name "${plugin.name}" is already registered.`);
      return;
    }
    this.plugins.set(plugin.name, plugin);
  }

  public getPlugin(name: string): IDevExPlugin | undefined {
    return this.plugins.get(name);
  }

  public getConfig(): DevExConfig {
    return this.config;
  }

  /**
   * Returns an array of all registered plugins.
   */
  public getPlugins(): IDevExPlugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * Returns a list of all registered recommendation rules and their current configurations.
   * Useful for building a settings UI.
   */
  public getRecommendationRulesWithConfig(): { rule: IRecommendationRule<any>; userConfig: import('./recommendations/RecommendationEngine').UserRuleConfig; }[] {
    const rules = this.recommendationEngine['rules']; // Access private field for this utility
    return rules.map(rule => ({
      rule,
      userConfig: this.config.recommendations?.[rule.id] ?? {},
    }));
  }

  /**
   * Subscribes to an event.
   * @param event The event name.
   * @param listener The callback function.
   */
  public on<K extends keyof DevExEventMap>(event: K, listener: (...args: DevExEventMap[K]) => void): void {
    if (!this.eventEmitter.has(event)) {
      this.eventEmitter.set(event, []);
    }
    this.eventEmitter.get(event)!.push(listener as (...args: any[]) => void);
  }

  /**
   * Emits an event to all subscribed listeners.
   * @param event The event name.
   * @param args The arguments to pass to the listeners.
   */
  public emit<K extends keyof DevExEventMap>(event: K, ...args: DevExEventMap[K]): void {
    if (this.eventEmitter.has(event)) {
      // Using try-catch to ensure one failing listener doesn't stop others.
      this.eventEmitter.get(event)!.forEach(listener => { try { listener(...args); } catch (e) { console.error(`Error in event listener for "${event}":`, e); }});
    }
  }

  public updateMetric(key: keyof DevExMetrics, value: number): void {
    if (key in this.metrics) {
      this.metrics[key] = value;
    }
  }

  public incrementMetric(key: 'errorCount' | 'warningCount'): void {
    if (key in this.metrics) {
      this.metrics[key]++;
    }
  }

  /**
   * Выполняет команду инструмента
   */
  async executeToolCommand(toolId: string, commandId: string): Promise<void> {
    const toolingPlugin = this.plugins.get('tooling') as ToolingPlugin | undefined;
    const tool = toolingPlugin?.getTools().find(t => t.id === toolId);
    if (!tool) {
      throw new Error(`Tool ${toolId} not found`);
    }

    const command = tool.commands.find(cmd => cmd.id === commandId);
    if (!command) {
      throw new Error(`Command ${commandId} not found in tool ${toolId}`);
    }

    console.log(`⚡ Executing: ${command.name}`);

    const startTime = performance.now();

    try {
      // В реальном проекте здесь будет выполнение команды через child_process
      await this.executeCommand(command.command, command.args);

      const duration = performance.now() - startTime;
      console.log(`✅ Command completed in ${duration.toFixed(2)}ms`);
    } catch (error) {
      console.error(`❌ Command failed:`, error);
      throw error;
    }
  }

  /**
   * Генерирует код по шаблону
   */
  generateCode(templateId: string, variables: Record<string, string>): string {
    const codeGenPlugin = this.plugins.get('code-generation') as CodeGenerationPlugin | undefined;
    if (!codeGenPlugin) {
      throw new Error('CodeGenerationPlugin is not registered or enabled.');
    }
    return codeGenPlugin.generateCode(templateId, variables);
  }

  /**
   * Получает метрики DevEx
   */
  getMetrics(): DevExMetrics {
    return { ...this.metrics };
  }

  /**
   * Получает ошибки
   */
  getErrors(): ErrorBoundary[] {
    const errorPlugin = this.plugins.get('error-handling') as ErrorHandlingPlugin | undefined;
    if (errorPlugin) {
      return errorPlugin.getErrors();
    }
    return [];
  }

  /**
   * Получает профили производительности
   */
  getPerformanceProfiles(): PerformanceProfile[] {
    const performancePlugin = this.plugins.get('performance') as
      | PerformancePlugin
      | undefined;
    return performancePlugin ? performancePlugin.getProfiles() : [];
  }

  /**
   * Очищает ошибки
   */
  clearErrors(): void {
    const errorPlugin = this.plugins.get('error-handling') as ErrorHandlingPlugin | undefined;
    if (errorPlugin) {
      errorPlugin.clearErrors();
      this.metrics.errorCount = 0;
    }
  }

  /**
   * Создает отчет о DevEx
   */
  generateDevExReport(): DevExReport {
    const toolingPlugin = this.plugins.get('tooling') as ToolingPlugin | undefined;
    const errorPlugin = this.plugins.get('error-handling') as ErrorHandlingPlugin | undefined;
    const performancePlugin = this.plugins.get('performance') as PerformancePlugin | undefined;

    const tools = toolingPlugin?.getTools() ?? [];
    const enabledTools = tools.filter(tool => tool.enabled);
    const errors = errorPlugin?.getErrors() ?? [];
    const profiles = performancePlugin?.getProfiles() ?? [];
    const pluginData: Record<string, any> = {};

    // Dynamically collect data from all reportable plugins
    this.plugins.forEach(plugin => {
      if (isReportable(plugin)) {
        const report = plugin.getReportData();
        if (report) {
          Object.assign(pluginData, report);
        }
      }
    });

    // First, build the report data without recommendations
    const reportData: Omit<DevExReport, 'recommendations'> = {
      timestamp: new Date(),
      metrics: this.getMetrics(),
      tools: {
        total: tools.length,
        enabled: enabledTools.length,
        categories: this.getToolCategories(tools),
      },
      errors: {
        total: errors.length,
        recent: errors.slice(-10),
      },
      performance: {
        profiles: profiles.slice(-10),
        averageMemoryUsage: performancePlugin?.calculateAverageMemoryUsage() ?? 0,
      },
      plugins: pluginData,
    };

    // Then, generate recommendations based on the complete report data
    const recommendations = this.recommendationEngine.generate({
      ...reportData,
      recommendations: [], // Pass dummy value to satisfy the type
    });

    // Finally, return the full report
    return { ...reportData, recommendations };
  }

  // Приватные методы

  private async executeCommand(command: string, args: string[]): Promise<void> {
    // Упрощенная реализация - в реальном проекте используйте child_process
    console.log(`Executing: ${command} ${args.join(' ')}`);
  }

  private getToolCategories(tools: DevelopmentTool[]): Record<string, number> {
    const categories: Record<string, number> = {};

    for (const tool of tools) {
      categories[tool.category] = (categories[tool.category] || 0) + 1;
    }

    return categories;
}
