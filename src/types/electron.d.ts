/**
 * Type definitions for the Electron APIs exposed via the preload script.
 * This file should be included in the `tsconfig.renderer.json` to provide
 * type safety and autocompletion for `window.electron`.
 */

export interface IElectronAPI {
  storage: {
    getItem: (key: string) => Promise<string | null>;
    setItem: (key: string, value: string) => Promise<void>;
    removeItem: (key: string) => Promise<void>;
    clear: () => Promise<void>;
    getAllKeys: () => Promise<string[]>;
  };
}

declare global {
  interface Window {
    electron: IElectronAPI;
  }
}