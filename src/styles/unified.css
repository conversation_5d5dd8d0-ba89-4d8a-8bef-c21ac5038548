/**
 * Unified Styles
 * Consolidates all duplicate CSS/SCSS files and variables from:
 * - src/renderer/styles.css
 * - src/shared/components/Button.module.css
 * - src/renderer/themes/_variables.css
 * - src/renderer/themes/light.css
 * - src/renderer/themes/modern-dark.css
 */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  background-color: var(--color-background);
  color: var(--color-text);
  overflow: hidden; /* Hide scrollbars */
  line-height: var(--line-height-normal);
  transition: background-color var(--transition-base), color var(--transition-base);
}

/* ============================================================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   ============================================================================ */

:root {
  /* Background Colors */
  --bg-color: #ffffff;
  --bg-secondary: #f7fafc;
  --bg-tertiary: #edf2f7;

  /* Text Colors */
  --text-color: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;

  /* Border Colors */
  --border-color: #e2e8f0;
  --border-secondary: #cbd5e0;
}

/* System Theme (follows OS preference) */
@media (prefers-color-scheme: dark) {
  body.theme-system {
    --bg-color: #1a202c;
  }
}

/* ============================================================================
   LAYOUT COMPONENTS
   ============================================================================ */

.browser-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* ============================================================================
   BUTTON COMPONENTS (from Button.module.css)
   ============================================================================ */

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  gap: var(--spacing-sm);
}

.button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

.button:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

/* Button Variants */
.button.primary {
  color: white;
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.button.primary:hover:not(:disabled) {
  background-color: #2c5282;
  border-color: #2c5282;
}

.button.secondary {
  color: var(--text-color);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

.button.secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
}

.button.outline {
  color: var(--text-color);
  background-color: transparent;
  border-color: var(--border-color);
}

.button.outline:hover:not(:disabled) {
  background-color: var(--bg-secondary);
}

.button.ghost {
  color: var(--text-color);
  background-color: transparent;
  border-color: transparent;
}

.button.ghost:hover:not(:disabled) {
  background-color: var(--bg-secondary);
}

.button.danger {
  color: white;
  background-color: var(--color-error);
  border-color: var(--color-error);
}

.button.danger:hover:not(:disabled) {
  background-color: #c53030;
  border-color: #c53030;
}

/* Button Sizes */
.button.xs {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
}

.button.sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.875rem;
}

.button.md {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 1rem;
}

.button.lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.125rem;
}

.button.xl {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 1.25rem;
}

/* ============================================================================
   UTILITY CLASSES
   ============================================================================ */

/* Spacing */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

/* Text */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Colors */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

/* Border Radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: 9999px; }

/* Shadows */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Transitions */
.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* Visibility */

:root {
  /* Color System - Primary */
  --color-primary: #2563eb;
  --color-primary-light: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-primary-hover: #1e40af;

  /* Color System - Secondary */
  --color-secondary: #64748b;
  --color-secondary-light: #94a3b8;
  --color-secondary-dark: #475569;
  --color-secondary-hover: #334155;

  /* Color System - Status */
  --color-success: #22c55e;
  --color-success-light: #4ade80;
  --color-success-dark: #16a34a;
  --color-warning: #f59e0b;
  --color-warning-light: #fbbf24;
  --color-warning-dark: #d97706;
  --color-error: #ef4444;
  --color-error-light: #f87171;
  --color-error-dark: #dc2626;
  --color-info: #3b82f6;
  --color-info-light: #60a5fa;
  --color-info-dark: #2563eb;

  /* Background Colors */
  --color-background: #ffffff;
  --color-background-secondary: #f8fafc;
  --color-background-tertiary: #f1f5f9;
  --color-surface: #ffffff;
  --color-surface-elevated: #ffffff;

  /* Text Colors */
  --color-text: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #ffffff;
  --color-text-muted: #64748b;

  /* Border Colors */
  --color-border: #e2e8f0;
  --color-border-light: #f1f5f9;
  --color-border-dark: #cbd5e1;
  --color-border-focus: var(--color-primary);

  /* Shadow Colors */
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-light: rgba(0, 0, 0, 0.05);
  --color-shadow-dark: rgba(0, 0, 0, 0.2);

  /* Spacing System */
  --spacing-0: 0;
  --spacing-1: 0.25rem;  /* 4px */
  --spacing-2: 0.5rem;   /* 8px */
  --spacing-3: 0.75rem;  /* 12px */
  --spacing-4: 1rem;     /* 16px */
  --spacing-5: 1.25rem;  /* 20px */
  --spacing-6: 1.5rem;   /* 24px */
  --spacing-8: 2rem;     /* 32px */
  --spacing-10: 2.5rem;  /* 40px */
  --spacing-12: 3rem;    /* 48px */
  --spacing-16: 4rem;    /* 64px */
  --spacing-20: 5rem;    /* 80px */
  --spacing-24: 6rem;    /* 96px */

  /* Border Radius */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;  /* 2px */
  --border-radius-md: 0.25rem;   /* 4px */
  --border-radius-lg: 0.5rem;    /* 8px */
  --border-radius-xl: 0.75rem;   /* 12px */
  --border-radius-2xl: 1rem;     /* 16px */
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Typography */
  --font-family-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  --font-family-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;

  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 250ms ease;
  --transition-slow: 350ms ease;
  --transition-all: all var(--transition-base);

  /* Z-Index Scale */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Animation Speeds */
  --animation-speed-fast: 0.15s;
  --animation-speed-base: 0.25s;
  --animation-speed-slow: 0.35s;
  --animation-speed-theme: 0.3s;
  --animation-speed-interface: 0.2s;

  /* Component Specific Variables */
  --toolbar-height: 48px;
  --sidebar-width: 240px;
  --tab-height: 36px;
  --address-bar-height: 40px;
}

/* ============================================================================
   DARK THEME
   ============================================================================ */

[data-theme="dark"], .dark-theme, body.theme-dark {
  /* Background Colors */
  --color-background: #0f172a;
  --color-background-secondary: #1e293b;
  --color-background-tertiary: #334155;
  --color-surface: #1e293b;
  --color-surface-elevated: #334155;

  /* Text Colors */
  --color-text: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #0f172a;
  --color-text-muted: #64748b;

  /* Border Colors */
  --color-border: #334155;
  --color-border-light: #475569;
  --color-border-dark: #1e293b;

  /* Shadow Colors */
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-light: rgba(0, 0, 0, 0.2);
  --color-shadow-dark: rgba(0, 0, 0, 0.5);

  /* Component adjustments for dark theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

/* ============================================================================
   SYSTEM THEME PREFERENCE
   ============================================================================ */

@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    /* Apply dark theme variables when system preference is dark */
    --color-background: #0f172a;
    --color-background-secondary: #1e293b;
    --color-background-tertiary: #334155;
    --color-surface: #1e293b;
    --color-surface-elevated: #334155;
    --color-text: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-tertiary: #94a3b8;
    --color-text-inverse: #0f172a;
    --color-border: #334155;
    --color-border-light: #475569;
    --color-border-dark: #1e293b;
    --color-shadow: rgba(0, 0, 0, 0.3);
  }
}

/* ============================================================================
   LEGACY VARIABLE ALIASES FOR BACKWARD COMPATIBILITY
   ============================================================================ */

/* ============================================================================
   BASE STYLES
   ============================================================================ */

/* ============================================================================
   UTILITY CLASSES
   ============================================================================ */

/* Spacing utilities */
.p-0 { padding: var(--spacing-0); }
.p-1 { padding: var(--spacing-1); } /* 4px */
.p-2 { padding: var(--spacing-2); } /* 8px */
.p-3 { padding: var(--spacing-3); } /* 12px */
.p-4 { padding: var(--spacing-4); } /* 16px */
.p-6 { padding: var(--spacing-6); } /* 24px */
.p-8 { padding: var(--spacing-8); } /* 32px */

.m-0 { margin: var(--spacing-0); }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

/* Text utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Color utilities */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-background-secondary); }
.bg-surface { background-color: var(--color-surface); }

/* Border utilities */
.border { border: 1px solid var(--color-border); }
.border-t { border-top: 1px solid var(--color-border); }
.border-b { border-bottom: 1px solid var(--color-border); }
.border-l { border-left: 1px solid var(--color-border); }
.border-r { border-right: 1px solid var(--color-border); }

.rounded { border-radius: var(--border-radius-md); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: var(--border-radius-full); }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Transition utilities */
.transition { transition: var(--transition-all); }
.transition-fast { transition-duration: var(--transition-fast); }
.transition-slow { transition-duration: var(--transition-slow); }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
