import { connect, StringCodec, type NatsConnection } from 'nats'

const natsUrl = process.env.NATS_URL ?? 'nats://localhost:4222'

const start = async (): Promise<void> => {
  let nc: NatsConnection
  try {
    nc = await connect({ servers: natsUrl })
    console.log(`Notification Service connected to NATS at ${nc.getServer()}`)
  } catch (err) {
    console.error('Failed to connect to NATS', err)
    process.exit(1)
  }

  const sc = StringCodec()

  // Создаем подписку на событие 'user:created'
  const sub = nc.subscribe('user:created')
  console.log("Subscribed to 'user:created'")

  // Асинхронно обрабатываем входящие сообщения
  ;(async () => {
    for await (const msg of sub) {
      const data = JSON.parse(sc.decode(msg.data))
      console.log(`[EVENT RECEIVED] User created: ID=${data.id}, Email=${data.email}`)
      console.log(`---> Simulating sending welcome email to ${data.email}...`)
    }
  })().catch(err => { console.error('Subscription error:', err) })

  // Graceful shutdown
  process.on('SIGINT', () => { nc.close().catch(() => {}) })
  process.on('SIGTERM', () => { nc.close().catch(() => {}) })
}

start().catch(err => {
  console.error('Notification service failed to start:', err)
})