{"name": "api-gateway", "version": "1.0.0", "description": "API Gateway for A14 Browser Ultimate", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest --testPathPattern=src/.*.test.ts", "test:integration": "jest --testPathPattern=src/.*.integration.test.ts"}, "dependencies": {"axios": "^1.6.8", "express": "^4.18.2", "http-proxy-middleware": "^3.0.0", "nats": "^2.19.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/jest": "^29.5.12", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^5.59.0", "eslint": "^8.38.0", "eslint-config-standard-with-typescript": "^34.0.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "supertest": "^7.0.0", "ts-jest": "^29.1.2", "ts-node-dev": "^2.0.0", "typescript": "^5.0.4"}}