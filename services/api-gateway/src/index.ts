import express, { type Request, type Response } from 'express'
import { createProxyMiddleware } from 'http-proxy-middleware'
import { connect, StringCodec } from 'nats'
import { authMiddleware } from './middleware/auth.middleware'

const app = express()
const port = process.env.PORT ?? 3000
const AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL ?? 'http://localhost:3001'
const natsUrl = process.env.NATS_URL ?? 'nats://localhost:4222'

// Проксируем все запросы к /auth/* на сервис аутентификации
app.use('/auth', createProxyMiddleware({
  target: AUTH_SERVICE_URL,
  changeOrigin: true
}))

// Публичный маршрут, не требующий аутентификации
app.get('/api/health', (req: Request, res: Response) => {
  res.send('A14 API Gateway is running!')
})

// Защищенный маршрут, который требует валидный JWT
app.get('/api/protected/data', authMiddleware, (req: Request, res: Response) => {
  // Благодаря authMiddleware, мы можем быть уверены, что запрос авторизован
  res.json({ message: 'This is protected data!', user: (req as any).user })
})

const start = async (): Promise<void> => {
  try {
    const nc = await connect({ servers: natsUrl })
    console.log(`API Gateway connected to NATS at ${nc.getServer()}`)

    // Создаем кодек для преобразования данных
    const sc = StringCodec()

    // Создаем подписку на событие 'user:created'
    const sub = nc.subscribe('user:created')
    console.log("Subscribed to 'user:created'")

    // Асинхронно обрабатываем входящие сообщения
    ;(async () => {
      for await (const msg of sub) {
        const data = JSON.parse(sc.decode(msg.data))
        console.log(`[EVENT RECEIVED] User created: ID=${data.id}, Email=${data.email}`)
      }
    })().catch(err => { console.error('Subscription error:', err) })

    // Graceful shutdown
    process.on('SIGINT', () => { nc.close().catch(() => {}) })
    process.on('SIGTERM', () => { nc.close().catch(() => {}) })
  } catch (err) {
    console.error('Failed to connect to NATS', err)
  }
}

const server = app.listen(port, () => { console.log(`API Gateway listening on port ${port}`); start().catch(e => console.error(e)) })

export { app, server }