import { type Request, type Response, type NextFunction } from 'express'
import axios from 'axios'

const AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL ?? 'http://localhost:3001'

export const authMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  const authHeader = req.headers.authorization

  if (authHeader === undefined || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'Authorization header is missing or malformed' })
  }

  const token = authHeader.split(' ')[1]

  try {
    const response = await axios.post(`${AUTH_SERVICE_URL}/auth/validate`, { token })

    if (response.data.valid === true) {
      // Опционально: добавляем информацию о пользователе в объект запроса
      // для использования в последующих обработчиках
      (req as any).user = response.data.decoded
      next()
    } else {
      return res.status(403).json({ message: 'Forbidden: Invalid token' })
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 401) {
      return res.status(403).json({ message: 'Forbidden: Invalid token' })
    }
    console.error('Error validating token:', error)
    return res.status(500).json({ message: 'Internal server error during token validation' })
  }
}