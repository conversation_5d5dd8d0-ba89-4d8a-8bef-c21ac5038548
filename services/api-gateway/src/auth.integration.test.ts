import request from 'supertest'
import { app, server } from './index'

// URL нашего API Gateway, который мы будем тестировать
const api = request(app)

// URL сервиса аутентификации для прямой очистки состояния
const authServiceUrl = process.env.AUTH_SERVICE_URL ?? 'http://localhost:3001'

describe('Auth Integration Flow', () => {
  // Перед каждым тестом очищаем хранилище пользователей в auth-service
  beforeEach(async () => {
    try {
      await request(authServiceUrl).post('/auth/test/clear')
    } catch (error) {
      console.error('Could not clear user store. Is auth-service running?', error)
      // Если сервис не доступен, нет смысла продолжать тесты
      throw new Error('Auth service is not available for test setup.')
    }
  })

  // После всех тестов останавливаем сервер, чтобы Jest мог завершиться
  afterAll((done) => {
    server.close(done)
  })

  it('should deny access to protected route without a token', async () => {
    const response = await api.get('/api/protected/data')
    expect(response.status).toBe(401)
  })

  it('should successfully register, login, and access a protected route', async () => {
    const userCredentials = {
      email: '<EMAIL>',
      password: 'password123'
    }

    // 1. Регистрация нового пользователя через API Gateway
    const registerResponse = await api
      .post('/auth/register')
      .send(userCredentials)

    expect(registerResponse.status).toBe(201)
    expect(registerResponse.body.message).toBe('User created successfully')

    // 2. Вход в систему для получения токена
    const loginResponse = await api
      .post('/auth/login')
      .send(userCredentials)

    expect(loginResponse.status).toBe(200)
    expect(loginResponse.body.token).toBeDefined()
    const token = loginResponse.body.token

    // 3. Доступ к защищенному маршруту с использованием токена
    const protectedResponse = await api
      .get('/api/protected/data')
      .set('Authorization', `Bearer ${token}`)

    expect(protectedResponse.status).toBe(200)
    expect(protectedResponse.body.message).toBe('This is protected data!')
    expect(protectedResponse.body.user).toBeDefined()
    expect(protectedResponse.body.user.email).toBe(userCredentials.email)
  })
})