{"name": "auth-service", "version": "1.0.0", "description": "Authentication Service for A14 Browser Ultimate", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "express": "^4.18.2", "nats": "^2.19.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.17", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@typescript-eslint/eslint-plugin": "^5.59.0", "eslint": "^8.38.0", "eslint-config-standard-with-typescript": "^34.0.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "jsonwebtoken": "^9.0.2", "ts-jest": "^29.1.2", "ts-node-dev": "^2.0.0", "typescript": "^5.0.4"}}