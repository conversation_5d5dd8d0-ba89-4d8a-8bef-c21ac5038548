import express from 'express'
import bodyParser from 'body-parser'
import { login, register, validateToken } from './controllers/auth.controller'
import { clearUsersForTesting } from './services/user.service'
import { natsClient } from './nats-client'

const app = express()
const port = process.env.PORT ?? 3001
const natsUrl = process.env.NATS_URL ?? 'nats://localhost:4222'

app.use(bodyParser.json())

app.get('/auth/health', (req, res) => {
  res.send('A14 Auth Service is running!')
})

// Эндпоинт для регистрации нового пользователя
app.post('/auth/register', register)

// Эндпоинт для входа и получения токена
app.post('/auth/login', login)

// Эндпоинт для валидации токена (используется другими сервисами)
app.post('/auth/validate', validateToken)

// Эндпоинт для очистки данных, доступный только в режиме разработки/тестирования
if (process.env.NODE_ENV === 'development') {
  console.log('Registering test-only /auth/test/clear endpoint')
  app.post('/auth/test/clear', (req, res) => {
    clearUsersForTesting()
    res.status(204).send()
  })
}

const start = async (): Promise<void> => {
  try {
    await natsClient.connect(natsUrl)
    console.log('Auth Service connected to NATS')

    // Graceful shutdown
    process.on('SIGINT', () => { natsClient.client.close().catch(() => {}) })
    process.on('SIGTERM', () => { natsClient.client.close().catch(() => {}) })
  } catch (err) {
    console.error('Failed to connect to NATS', err)
    process.exit(1)
  }

  app.listen(port, () => {
    console.log(`Auth Service listening on port ${port}`)
    console.log(`Current NODE_ENV: ${process.env.NODE_ENV}`)
  })
}

const server = app.listen(port, () => {
  start().catch(err => { console.error(err) })
})

export { app, server }