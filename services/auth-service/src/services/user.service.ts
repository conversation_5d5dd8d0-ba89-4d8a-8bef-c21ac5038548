import bcrypt from 'bcryptjs'
import { type User } from '../types'

// Внимание: Это простое хранилище в памяти для демонстрации.
// В реальном приложении здесь будет использоваться база данных (например, PostgreSQL, MongoDB).
const users: User[] = []
let userIdCounter = 1

export const findUserByEmail = (email: string): User | undefined => {
  return users.find(user => user.email === email)
}

export const findUserById = (id: number): User | undefined => {
  return users.find(user => user.id === id)
}

export const createUser = async (email: string, password: string): Promise<User> => {
  const passwordHash = await bcrypt.hash(password, 10)
  const newUser: User = {
    id: userIdCounter++,
    email,
    passwordHash
  }
  users.push(newUser)
  return newUser
}

export const clearUsersForTesting = (): void => {
  users.length = 0
  userIdCounter = 1
  console.log('In-memory user store cleared for testing.')
}