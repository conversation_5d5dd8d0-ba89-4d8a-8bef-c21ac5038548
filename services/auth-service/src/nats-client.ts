import { connect, type NatsConnection } from 'nats'

class NatsClient {
  private _client?: NatsConnection

  get client (): NatsConnection {
    if (this._client === undefined) {
      throw new Error('Cannot access NATS client before connecting')
    }
    return this._client
  }

  async connect (url: string): Promise<void> {
    try {
      this._client = await connect({ servers: url })
      console.log(`Connected to NATS at ${this.client.getServer()}`)

      // Обеспечиваем корректное закрытие соединения
      this.client.closed().then((err) => {
        console.log(`NATS connection closed. ${err?.message ?? ''}`)
      }).catch(() => {})
    } catch (err) {
      console.error('Error connecting to NATS', err)
      throw err
    }
  }
}

export const natsClient = new NatsClient()