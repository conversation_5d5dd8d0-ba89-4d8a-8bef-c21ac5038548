import { type Request, type Response } from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { createUser, findUserByEmail } from '../services/user.service'
import { JWT_SECRET } from '../config'
import { natsClient } from '../nats-client'
import { StringCodec } from 'nats'

export const register = async (req: Request, res: Response): Promise<any> => {
  const { email, password } = req.body
  if (email === undefined || password === undefined) {
    return res.status(400).json({ message: 'Email and password are required' })
  }

  try {
    const existingUser = findUserByEmail(email)
    if (existingUser != null) {
      return res.status(409).json({ message: 'User already exists' })
    }

    const user = await createUser(email, password)

    // Публикуем событие о создании пользователя
    const sc = StringCodec()
    natsClient.client.publish('user:created', sc.encode(JSON.stringify({
      id: user.id,
      email: user.email
    })))
    console.log(`Event 'user:created' published for user ${user.email}`)

    return res.status(201).json({ message: 'User created successfully', userId: user.id })
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error' })
  }
}

export const login = async (req: Request, res: Response): Promise<any> => {
  const { email, password } = req.body
  if (email === undefined || password === undefined) {
    return res.status(400).json({ message: 'Email and password are required' })
  }

  const user = findUserByEmail(email)
  if (user == null) {
    return res.status(401).json({ message: 'Invalid credentials' })
  }

  const isPasswordValid = await bcrypt.compare(password, user.passwordHash)
  if (!isPasswordValid) {
    return res.status(401).json({ message: 'Invalid credentials' })
  }

  const token = jwt.sign({ userId: user.id, email: user.email }, JWT_SECRET, { expiresIn: '1h' })
  return res.json({ token })
}

export const validateToken = (req: Request, res: Response): any => {
  const { token } = req.body
  if (token === undefined) {
    return res.status(400).json({ message: 'Token is required' })
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET)
    return res.json({ valid: true, decoded })
  } catch (error) {
    return res.status(401).json({ valid: false, message: 'Invalid token' })
  }
}