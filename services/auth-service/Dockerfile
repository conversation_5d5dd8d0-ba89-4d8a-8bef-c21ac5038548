# Этап сборки
FROM node:18-alpine AS builder
WORKDIR /usr/src/app

COPY package*.json ./ 
RUN npm install --only=production

COPY . . 
RUN npm run build

# Этап запуска
FROM node:18-alpine
WORKDIR /usr/src/app

COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules 
COPY package*.json ./ 

# Устанавливаем dev-зависимости для hot-reloading (например, ts-node-dev)
RUN npm install

CMD [ "node", "dist/index.js" ]