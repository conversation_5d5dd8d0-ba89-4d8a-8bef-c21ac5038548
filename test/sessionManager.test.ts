import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SessionManager } from '../src/main/session/sessionManager'; // Путь нужно будет проверить

// Мок для store, если он не передан
const mockStore = {
  get: vi.fn(),
  set: vi.fn(),
};

describe('SessionManager Validation Tests', () => {
  let manager: SessionManager;
  const validEncryptionKey = 'a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6';

  beforeEach(() => {
    // @ts-ignore - SessionManager может быть не экспортирован как класс с конструктором
    manager = new SessionManager({ store: mockStore });
    manager.encryptionKey = validEncryptionKey;
  });

  describe('validateEncryptionKey()', () => {
    it('Должен выбрасывать ошибку при отсутствии ключа', () => {
      manager.encryptionKey = undefined;
      expect(() => manager.validateEncryptionKey()).toThrow('Ключ шифрования не инициализирован');
    });

    it('Должен выбрасывать ошибку при сохранении без ключа', async () => {
      manager.encryptionKey = undefined;
      await expect(manager.saveSession({ version: '1.0', tabs: [] })).rejects.toThrow(
        /Ключ шифрования не инициализирован/
      );
    });

    it('Должен проверять наличие hex-символов в ключе', () => {
      manager.encryptionKey = 'g1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6'; // 'g' is not a hex char
      expect(() => manager.validateEncryptionKey()).toThrow(/Некорректный формат ключа/);
    });

    it('Должен проверять формат hex-строки 64 символа', () => {
      manager.encryptionKey = 'a1b2c3';
      expect(() => manager.validateEncryptionKey()).toThrow(/Некорректный формат ключа/);
    });
  });

  describe('validateSessionStructure()', () => {
    const validSession = {
      version: '1.0',
      tabs: [{ url: 'https://example.com', title: 'Example' }],
    };

    it('Должен пропускать валидную сессию', () => {
      expect(() => manager.validateSessionStructure(validSession)).not.toThrow();
    });

    it('Должен обрабатывать полностью пустую сессию', () => {
      expect(() => manager.validateSessionStructure(null)).toThrow(/Структура сессии не соответствует формату/);
    });

    it('Должен проверять наличие версии', () => {
      const invalid = { ...validSession, version: undefined };
      expect(() => manager.validateSessionStructure(invalid)).toThrow(/Отсутствует поле version/);
    });

    it('Должен проверять тип массива вкладок', () => {
      const invalid = { ...validSession, tabs: {} };
      expect(() => manager.validateSessionStructure(invalid)).toThrow(/Поле tabs должно быть массивом/);
    });

    it('Должен проверять структуру вкладок', () => {
      const invalid = { ...validSession, tabs: [{ url: null }] };
      expect(() => manager.validateSessionStructure(invalid)).toThrow(/отсутствует или некорректен URL/);
    });
  });

  describe('Integration Tests', () => {
    it('Должен корректно шифровать и дешифровать сессию', async () => {
      const session = {
        version: '1.0',
        tabs: [{ url: 'https://secure.com', title: 'Secure' }],
      };

      const encrypted = await manager.saveSession(session);
      const decrypted = await manager.restoreSession(encrypted);

      expect(decrypted).toEqual(session);
    });

    it('Должен выбрасывать ошибку при несовпадении ключа', async () => {
      const originalSession = { version: '1.0', tabs: [] };
      const encrypted = await manager.saveSession(originalSession);

      manager.encryptionKey = '0'.repeat(64); // Другой ключ

      await expect(manager.restoreSession(encrypted)).rejects.toThrow(/Ошибка дешифрования/);
    });

    it('Должен выбрасывать ошибку при повреждённых данных сессии', async () => {
      const session = { version: '1.0', tabs: [] };
      const encrypted = await manager.saveSession(session);
      const corrupted = encrypted.slice(0, -10) + '0000';

      await expect(manager.restoreSession(corrupted)).rejects.toThrow(/Ошибка дешифрования/);
    });

    it('Должен восстанавливать сессию через новый экземпляр менеджера', async () => {
      const session = {
        version: '1.0',
        tabs: [{ url: 'https://restart.com', title: 'Restart Test' }],
      };

      const encrypted = await manager.saveSession(session);

      // Создаем новый менеджер с теми же настройками
      const newManager = new SessionManager({
        store: manager.store,
        encryptionKey: manager.encryptionKey,
      });

      const restored = await newManager.restoreSession(encrypted);
      expect(restored).toEqual(session);
    });
  });
});