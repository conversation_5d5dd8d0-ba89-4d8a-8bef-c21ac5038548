name: A14 CI Pipeline

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

env:
  NODE_VERSION: 18.x

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install all dependencies
        run: npm ci
      - name: Run linting for all workspaces
        # -ws: runs the command in all workspaces
        # --if-present: ignores workspaces without a "lint" script
        run: npm run lint -ws --if-present

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install all dependencies
        run: npm ci
      - name: Run NPM Audit
        # Fails the build only for high or critical severity vulnerabilities
        run: npm audit --audit-level=high

  codeql-analysis:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, typescript
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  test-and-build:
    name: Test & Build
    needs: [lint, security-audit]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false # Continue testing other services even if one fails
      matrix:
        service: [api-gateway, auth-service, notification-service]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install all dependencies
        run: npm ci
      - name: Run Tests for ${{ matrix.service }}
        # --workspace=... runs the command in a specific workspace
        run: npm test --workspace=services/${{ matrix.service }}
      - name: Build ${{ matrix.service }}
        run: npm run build --workspace=services/${{ matrix.service }}